# Generated by Django 4.2.1 on 2025-08-01 16:21

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0114_school_school_cycle'),
    ]

    operations = [
        migrations.AddField(
            model_name='year',
            name='school_fees_esia',
            field=models.PositiveSmallIntegerField(default=0, validators=[django.core.validators.MinValueValidator(20000)], verbose_name='droit annuel ESIA'),
        ),
        migrations.AlterField(
            model_name='year',
            name='school_fees',
            field=models.PositiveSmallIntegerField(default=5000, validators=[django.core.validators.MinValueValidator(100)], verbose_name='droit annuel ECC'),
        ),
    ]
