﻿from django.db import models
from django.contrib.auth import get_user_model, hashers, models as auth_models
from django.db.models import (
    Count, When, IntegerField, Case, Q, Sum, Subquery, OuterRef) 
from django.conf import settings
from django.core.validators import MinValueValidator
from decouple import config
import cloudinary
from cloudinary.models import CloudinaryField
from django.core.cache import cache
from django.db.models.query import QuerySet
from django.utils.translation import gettext_lazy as _
from project_utils import model_utils, constants, custom_utils

cloudinary.config(
    cloud_name=config('CLOUDINARY_CLOUD_NAME'),
    api_key=config('CLOUDINARY_API_KEY'),
    api_secret=config('CLOUDINARY_SECRET_KEY')
)

CLOUDINARY_TRANFORMATIONS = {
    'gravity':'faces', 'quality': 'auto',
    'crop': 'thumb', 'width': 300, 'height': 300,
    'effect':'improve:outdoor', 'zoom': 0.75
}


class LocalCommissionManager(models.Manager):
    def get_queryset(self):
        cached = cache.get('commissions_list')
        if cached:
            return cached
        qs = super().get_queryset().select_related('user')
        cache.set('commissions_list', qs, timeout=60 * 10)
        return qs
    
    def get_active_commissions(self):
        return self.get_queryset().filter(active=True)
    
    def get_with_results(self, exam=constants.EXAM_CEPE):
        year = custom_utils.get_current_year()
        min_avg = year.get_min_avg()
        return self.get_queryset().annotate(
            candidates=Count('center__enrollment', filter=Q(center__exam=exam) & Q(center__year=year), distinct=True), 
            admitted=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__average__gte=10) & Q(center__exam=exam) & Q(center__year=year), 
                distinct=True),
            admissible=Count(
                'center__enrollment', 
                filter=(
                    Q(center__enrollment__favoured=True) | \
                    (Q(center__enrollment__average__lt=10) & \
                    Q(center__enrollment__average__gte=min_avg))
                ) & \
                Q(center__exam=exam) & Q(center__year=year), 
                distinct=True),
            failed=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__average__lt=min_avg) & \
                       Q(center__exam=exam) & Q(center__year=year), 
                distinct=True),
            all_admitted=Count(
                'center__enrollment', 
                filter=(Q(center__enrollment__average__gte=min_avg) | \
                       Q(center__enrollment__favoured=True)) \
                       & Q(center__exam=exam) & Q(center__year=year), 
                distinct=True),
            present=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__average__gt=1) & Q(center__exam=exam) & Q(center__year=year), 
                distinct=True),
            missing=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__average__lte=1) & Q(center__exam=exam) & Q(center__year=year), 
                distinct=True),
            perc=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__average__gte=10) & Q(center__exam=exam) & Q(center__year=year), 
                distinct=True) / Count(
                'center__enrollment', 
                filter=Q(center__enrollment__average__gt=1) & Q(center__exam=exam) & Q(center__year=year), 
                distinct=True) * 100.0
        )
    def get_with_detailed_results(self, exam=constants.EXAM_CEPE):
        year = custom_utils.get_current_year()
        min_avg = 10 #year.get_min_avg()
        return self.get_queryset().annotate(
            boys_count=Count('center__enrollment', 
                             filter=Q(center__enrollment__student__gender=constants.GENDER_MALE) & \
                                    Q(center__exam=exam) & Q(center__year=year), 
                             distinct=True), 
            girls_count=Count('center__enrollment', 
                             filter=Q(center__enrollment__student__gender=constants.GENDER_FEMALE) & \
                                    Q(center__exam=exam) & Q(center__year=year), 
                             distinct=True), 
            students_count=Count('center__enrollment', filter=Q(center__exam=exam) & Q(center__year=year), distinct=True), 
            boys_admitted=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__gen_average__gte=min_avg) & \
                Q(center__enrollment__student__gender=constants.GENDER_MALE) & Q(center__exam=exam) \
                & Q(center__year=year), 
                distinct=True),
            girls_admitted=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__gen_average__gte=min_avg) & \
                Q(center__enrollment__student__gender=constants.GENDER_FEMALE) & Q(center__exam=exam) \
                & Q(center__year=year), 
                distinct=True),
            students_admitted=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__gen_average__gte=min_avg) & \
                    Q(center__exam=exam), distinct=True) \
                    & Q(center__year=year),
            boys_present=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__average__gt=1) & \
                Q(center__enrollment__student__gender=constants.GENDER_MALE) \
                & Q(center__exam=exam) & Q(center__year=year), 
                distinct=True),
            girls_present=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__average__gt=1) & \
                Q(center__enrollment__student__gender=constants.GENDER_FEMALE) & \
                Q(center__exam=exam) & Q(center__year=year), 
                distinct=True),
            students_present=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__average__gt=1) & Q(center__exam=exam) & Q(center__year=year), 
                distinct=True),
            boys_perc=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__gen_average__gte=min_avg) & \
                        Q(center__enrollment__student__gender=constants.GENDER_MALE) &
                        Q(center__exam=exam) & Q(center__year=year), distinct=True) / Count(
                'center__enrollment', 
                filter=Q(center__enrollment__gen_average__gt=1) & \
                        Q(center__enrollment__student__gender=constants.GENDER_MALE) & 
                        Q(center__exam=exam) & Q(center__year=year), 
                distinct=True) * 100.0,
            girls_perc=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__gen_average__gte=min_avg) & \
                        Q(center__enrollment__student__gender=constants.GENDER_FEMALE) \
                        & Q(center__exam=exam), distinct=True) & Q(center__year=year) / Count('center__enrollment', 
                filter=Q(center__enrollment__gen_average__gt=1) & \
                       Q(center__enrollment__student__gender=constants.GENDER_FEMALE) & 
                       Q(center__exam=exam) & Q(center__year=year), 
                distinct=True) * 100.0,
            students_perc=Count(
                'center__enrollment', 
                filter=Q(center__enrollment__gen_average__gte=min_avg) & \
                       Q(center__exam=exam), distinct=True) & Q(center__year=year) / Count(
                'center__enrollment', 
                filter=Q(center__enrollment__gen_average__gt=1) & Q(center__exam=exam) & Q(center__year=year), 
                distinct=True) * 100.0
        )
    
class LocalCommission(models.Model):
    translation = models.CharField('Traduction arabe', max_length=255, null=True)
    location = models.CharField(max_length=255, unique=True)
    phone = models.CharField(max_length=10)
    active = models.BooleanField('actif', default=True)
    initial_password = models.CharField('mot de passe', max_length=255)
    user = models.OneToOneField(settings.AUTH_USER_MODEL, 
        on_delete=models.PROTECT, verbose_name="compte d'utilisateur")
    objects = LocalCommissionManager()

    def get_full_name(self):
        return f'{self.user.last_name} {self.user.first_name}'
    
    def get_schools_count(self):
        return self.school_set.count()
    
    def __str__(self):
        return str(self.location).upper()
    
    def save(self, *args, **kwargs):
        cache_key = 'commissions_list'
        saved = super().save(*args, **kwargs)
        if cache.get(cache_key):
            cache.delete(cache_key)
        return saved
    
    class Meta:
        unique_together = [
            ['location', 'user'],
        ]
        verbose_name = 'commission locale'
        verbose_name_plural = 'commissions locales'


class SchoolManager(models.Manager):
    def get_queryset(self):
        cache_key = 'schools_list'
        cached = cache.get(cache_key)
        if cached:
            return cached   
        qs = super().get_queryset().filter(active=True) \
            .select_related('local_commission').order_by('name')
        cache.set(cache_key, qs, timeout=60 * 10)
        return qs.select_related('drena_obj')
    
    def get_for_user(self, user):
        if user.role == constants.ROLE_ECOLE and user.school:
            return self.get_queryset().filter(id=user.school.id)
        if user.role == constants.ROLE_COMMISSION_LOCALE:
            return self.get_queryset().filter(
                local_commission=user.localcommission)
        return self.get_queryset()
    
    def confirmed(self, user):
        return self.get_for_user(user).filter(confirmed=True)
    
    def get_for_user_with_students_count(self, *args, **kwargs):
        if not kwargs.get('year'):
            year = custom_utils.get_current_year()
            
        qs = self.get_for_user(*args, **kwargs)
        return qs.annotate(
            cepe_students_count=Count(
                Case( When(
                        Q(enrollment__year=year) & \
                        Q(enrollment__confirmed=True) & \
                        Q(enrollment__exam=constants.EXAM_CEPE), 
                        then=1), output_field=IntegerField()),
                ),
            bepc_students_count=Count(
                Case( When(
                        Q(enrollment__year=year) & \
                        Q(enrollment__confirmed=True) & \
                        Q(enrollment__exam=constants.EXAM_BEPC), 
                        then=1), output_field=IntegerField()),
                ),
            bac_students_count=Count(
                Case( When(
                        Q(enrollment__year=year) & \
                        Q(enrollment__confirmed=True) & \
                        Q(enrollment__exam=constants.EXAM_BAC), 
                        then=1), output_field=IntegerField()),
                ),
            students_count=Count(
                Case( When(
                        Q(enrollment__year=year) & \
                        Q(enrollment__confirmed=True), 
                        then=1), output_field=IntegerField()),
                ),
            cepe_enrolled=Count(
                Case( When(
                        Q(enrollment__year=year) & \
                        Q(enrollment__exam=constants.EXAM_CEPE), 
                        then=1), output_field=IntegerField()),
                ),
            bepc_enrolled=Count(
                Case( When(
                        Q(enrollment__year=year) & \
                        Q(enrollment__exam=constants.EXAM_BEPC), 
                        then=1), output_field=IntegerField()),
                ),
            bac_enrolled=Count(
                Case( When(
                        Q(enrollment__year=year) & \
                        Q(enrollment__exam=constants.EXAM_BAC), 
                        then=1), output_field=IntegerField()),
                ),
            students_enrolled=Count(
                Case( 
                    When(Q(enrollment__year=year), then=1), output_field=IntegerField()),
                ),
        )

    def get_payments_total(self, year=None):
        if not year:
            # Note: Model methods don't have request context for year selection
            year = custom_utils.get_current_year()
        return self.payment_set.filter(year=year).aggregate(
            total=Sum('amount')
        )['total'] or 0


class School(model_utils.TimeStampedModel):
    """ Represents a school """
    identifier = models.CharField(_('identifiant'), max_length=3, 
        null=True, blank=False, unique=True)
    name = models.CharField(_('nom en français'), max_length=255)
    name_ar = models.CharField(_('nom en arabe'), max_length=255, null=True, 
        blank=True)
    location = models.CharField(max_length=255, blank=True, null=True)
    director = models.CharField(_('directeur'), max_length=255)
    phone = models.CharField(_('contact'), max_length=10, 
        help_text=_(constants.WHATSAPP_NUMBER_HELP_TEXT))
    local_commission = models.ForeignKey(LocalCommission, 
        on_delete=models.PROTECT, null=True)
    active = models.BooleanField(default=True)
    drena = models.CharField(_('DRENA'), max_length=255, null=True)
    drena_obj = models.ForeignKey('Location', on_delete=models.SET_NULL, 
        null=True, blank=True, verbose_name='drena')
    iepp = models.CharField(_('IEPP'), max_length=255, null=True)
    school_centers = models.ManyToManyField(
        'Center', related_name='center_schools', blank=True)

    confirmed = models.BooleanField(default=False)
    user_created = models.BooleanField(default=False)
    
    objects = SchoolManager()
    
    # New fields
    school_type = models.CharField(_("Choisir le type d'école (ECC ou ESIA)"), max_length=4, 
        choices=constants.SCHOOL_TYPES, null=True, blank=False)
    school_status = models.CharField(_("Statut de l'école (Reconnu / Non rec.)"), max_length=2, 
        choices=constants.SCHOOL_STATUS_CHOICES, null=True, blank=False)
    school_cycle = models.CharField(_("Cycle de l'école"), max_length=1, 
        choices=constants.SCHOOL_CYCLE_CHOICIES, null=True, blank=False)
    teachers_count = models.PositiveSmallIntegerField(_("nombre d'enseignants"), 
        null=True, blank=False, validators=[MinValueValidator(1)])
    information_updated = models.BooleanField(default=False)
    def get_candidates(self, year=None, exam=None, confirmed=True):
        if not year:
            year = custom_utils.get_current_year()
        
        return Enrollment.candidates.get_candidates(
            year=year, exam=exam, confirmed=confirmed, school=self)
        
    def count_candidates(self, year, exam=None, confirmed=True):
        return self.get_candidates(year, exam, confirmed=confirmed).count()

    def count_cepe_candidates(self):
        year = custom_utils.get_current_year()
        return self.count_candidates(year=year, exam=constants.EXAM_CEPE, confirmed=True)
    
    def count_bepc_candidates(self, year, confirmed=True):
        return self.count_candidates(year, exam=constants.EXAM_BEPC, confirmed=confirmed)

    def get_exam_fees(self, year, exam, user):
        return custom_utils.get_exam_fees(year=year, exam=exam, user=user)
    
    def save(self, *args, **kwargs):
        cache_key = 'schools_list'
        if cache.get(cache_key):
            cache.delete(cache_key)
        cache.delete(cache_key)
        
        if not self.identifier:
            super().save(*args, **kwargs)
            self.identifier = custom_utils.generate_school_identifier(self.id)

        if self.confirmed and not self.user_created:
            queryset = get_user_model().objects.filter(username=self.identifier) 
            if not queryset.exists():
                user = get_user_model().objects.create(
                    username=self.identifier, 
                    password=hashers.make_password(str(self.phone).strip()), 
                    role=constants.ROLE_ECOLE, 
                    school=self)

                # Add to ecole group
                group, created = auth_models.Group.objects \
                    .get_or_create(name__iexact='Ecole')
                group.user_set.add(user)
                self.user_created = True
            else:
                user = queryset.first()
                user.password=hashers.make_password(str(self.phone).strip())
                user.save()

        return super().save(*args, **kwargs)

    class Meta:
        verbose_name = _('école')
        permissions = (
            ('change_school_status', 'can change school status'),
        )
        ordering = ['name']

    def __str__(self):
        name = self.name.upper().strip()
        return f'{name}'

    def natural_key(self):
        return self.__str__()
        

class Student(model_utils.TimeStampedModel):
    identifier = models.CharField(_('identifiant'), unique=True, 
        max_length=15, null=True)
    matricule = models.CharField(
        _('matricule DSPS'), null=True, blank=True, 
        max_length=10, help_text=_("Laisser vide si l'élève n'en a pas"))
    last_name = models.CharField(_('nom en français'), max_length=255)
    first_name = models.CharField(_('prénoms en français'), max_length=255)
    full_name_ar = models.CharField(_('nom et prénoms en arabe'),
        max_length=255, null=True, blank=True)
    gender = models.CharField(_('sexe'), max_length=1, 
        choices=constants.GENDER_CHOICES, default=constants.GENDER_MALE)
    birth_date = models.DateField(_('date de naissance'))
    birth_place = models.CharField(_('lieu de naissance'), max_length=255)
    birth_place_ar = models.CharField(_('lieu de naissance en arabe'), max_length=255)
    student_phone = models.CharField(_("contact de l'élève"), max_length=255, 
        null=True, blank=True, 
        help_text=_(constants.WHATSAPP_NUMBER_HELP_TEXT))
    father = models.CharField(_('père ou tuteur'), max_length=255)
    mother = models.CharField(_('mère'), max_length=255)
    parent_phone = models.CharField(_('contact du parent'), max_length=255, 
        null=True, blank=True, 
        help_text=_(constants.WHATSAPP_NUMBER_HELP_TEXT))
    school = models.ForeignKey(School, on_delete=models.PROTECT)
    photo = CloudinaryField(
        'image', folder='cherifla/students/', 
        transformation={
            **CLOUDINARY_TRANFORMATIONS
        },
        null=True, blank=True)
    certificate = CloudinaryField(
        'image', folder='cherifla/certificates/', 
        null=True, blank=True)
    nationality = models.CharField(max_length=2, 
        choices=constants.NATIONALITY_CHOICES, 
        default=constants.NATIONALITY_IVORIAN)

    class Meta:
        verbose_name = _('élève')

    def get_full_name(self, language=constants.LANGUAGE_FRENCH):
        if language == constants.LANGUAGE_FRENCH:
            return self.__str__()
        return f"{self.full_name_ar or ''}"

    def __str__(self):
        return f'{self.last_name} {self.first_name}'.upper()
    
    def save(self, exam=constants.EXAM_CEPE, *args):
        saved = super().save(*args)

        was_changed = False
        if not self.identifier:
            self.identifier = custom_utils.generate_identifier(
                    student_id=self.id, exam=exam
                )
            was_changed = True

        if was_changed: 
            return super().save(update_fields=['photo', 'identifier'])
        return saved
            
    
class Year(models.Model):
    name = models.CharField(_('année scolaire'), max_length=9, 
        help_text=_('Exemple: 2022-2023'))
    short_name = models.CharField(_('abbréviation'), max_length=4, 
        help_text=_('Exemple: 2023 pour 2022-2023'))
    translation = models.CharField(_('traduction'), max_length=10, 
        null=True, blank=True, default='')
    comment = models.CharField(max_length=255, null=True, blank=True)
    is_current = models.BooleanField(
        _('année en cours ?'), null=True, blank=True)
    price_cepe = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(100)]
    )
    price_bepc = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(100)]
    )
    price_bac = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(100)]
    )
    school_fees = models.PositiveSmallIntegerField(
        validators=[(MinValueValidator(100))]
    )
    school_message = models.TextField(_('message pour écoles'), 
        null=True, blank=True)
    commissions_message = models.TextField(_('message pour commissions'), 
        null=True, blank=True)
    can_add_student = models.BooleanField(default=True)
    can_edit_student = models.BooleanField(default=True)
    can_add_school = models.BooleanField(default=True)
    can_edit_school = models.BooleanField(default=True)
    can_confirm_student = models.BooleanField(default=True)
    can_view_grade = models.BooleanField(default=False)
    can_edit_grade = models.BooleanField(default=False)
    can_print_reports = models.BooleanField(default=False)
    can_edit_correction = models.BooleanField(default=False)
    can_edit_mock_grade = models.BooleanField(default=False)
    cepe_exam_date = models.DateField(null=True)
    bepc_exam_date = models.DateField(null=True)
    bac_exam_date = models.DateField(null=True)
    min_avg_cepe = models.FloatField(default=10)
    min_avg_bepc = models.FloatField(default=10)
    min_avg_bac = models.FloatField(default=10)
    report_date = models.CharField(max_length=255, default='')
    stats_required = models.BooleanField(default=False)
    stats_required_schools = models.ManyToManyField('School', blank=True)
    def get_price_for(self, exam):
        if exam == constants.EXAM_CEPE:
            return self.price_cepe
        elif exam == constants.EXAM_BEPC:
            return self.price_bepc
        elif exam == constants.EXAM_BAC:
            return self.price_bac
        return 0
    
    def add_perm(self, group: auth_models.Group, perm_code: str):
        group.permissions.add(
            auth_models.Permission.objects.get(codename=perm_code))
    
    def remove_perm(self, group: auth_models.Group, perm_code: str):
        group.permissions.remove(
            auth_models.Permission.objects.get(codename=perm_code))
    
    def save(self, *args, **kwargs):
        saved = super().save(*args, **kwargs)
        if self.is_current:
            cache.set('current_year', self, 60 * 10)
        
        school_group = auth_models.Group.objects.get(name__iexact='ecole')
        commission_group = auth_models.Group.objects.get(name__iexact='commission locale')

        # Student creation permissions
        if self.can_add_student:
            self.add_perm(school_group, 'add_student')
            self.add_perm(school_group, 'add_enrollment')
        else:
            self.remove_perm(school_group, 'add_student')
            self.remove_perm(school_group, 'add_enrollment')
        
        # Student edition permissions
        if self.can_edit_student:
            self.add_perm(school_group, 'change_student')
            self.add_perm(school_group, 'change_enrollment')
        else:
            self.remove_perm(school_group, 'change_student')
            self.remove_perm(school_group, 'change_enrollment')
        
        # School creation permissions
        if self.can_add_school:
            self.add_perm(commission_group, 'add_school')
        else:
            self.remove_perm(commission_group, 'add_school')
        
        # School creation permissions
        if self.can_edit_mock_grade:
            self.add_perm(commission_group, 'edit_mock_exam_grade')
        else:
            self.remove_perm(commission_group, 'edit_mock_exam_grade')

        # School edition permissions
        if self.can_edit_school:
            self.add_perm(commission_group, 'change_school')
        else:
            self.remove_perm(commission_group, 'change_school')
        
        if self.can_confirm_student:
            self.add_perm(commission_group, 'change_student_status')
        else:
            self.remove_perm(commission_group, 'change_student_status')
       
        #  Geade perms
        if self.can_edit_grade:
            self.add_perm(commission_group, 'change_grade')
        else:
            self.remove_perm(commission_group, 'change_grade')
        

        if self.can_edit_correction:
            self.add_perm(school_group, 'add_studentcorrection')
        else:
            self.remove_perm(school_group, 'add_studentcorrection')
        return saved
       

    def get_min_avg(self, exam=constants.EXAM_CEPE):
        if exam == constants.EXAM_CEPE:
            return self.min_avg_cepe
        elif exam == constants.EXAM_BEPC:
            return self.min_avg_bepc
        elif exam == constants.EXAM_BAC:
            return self.min_avg_bac
        return 9.5

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('année scolaire')
        verbose_name_plural  = _('années scolaires')



class Issue(models.Model):
    label = models.CharField(max_length=255)

    class Meta:
        verbose_name = 'problème'
        verbose_name_plural = 'problèmes'
        permissions = (
            ('resolve_issue', 'can resolve issue'),
        )

    def __str__(self):
        return f'{self.label}'


class CandidatesManager(models.Manager):
    def get_candidates(self, year, user=None, exam=None, 
                       confirmed=True, school=None, select_related=True):
        # Returns a qs of candidates depending on user role
        qs = self

        if school:
            qs = qs.filter(school=school, year=year)
        elif user.role == constants.ROLE_ECOLE:
            qs = qs.filter(year=year, school=user.school, active=True)
        elif user.role == constants.ROLE_COMMISSION_LOCALE:
            qs = qs.filter(
                year=year, active=True, 
                school__local_commission=user.localcommission
            )
        elif user.role == constants.ROLE_COMMISSION_NATIONALE:
            qs = qs.filter(year=year, active=True)

        if exam:
            qs = qs.filter(exam=exam)
        if confirmed:
            qs = qs.filter(confirmed=True)
        
        if select_related:
            qs = qs.select_related('student', 'school', 'school__local_commission')\
                    .order_by('-date_created')
        return qs.annotate(card_status=Subquery(
            StudentCard.objects.filter(enrollment=OuterRef('pk')).only('status').values('status')[:1]
        ))
    
    def unconfirmed(self, *args, **kwargs):
        return self.get_candidates(*args, **kwargs).filter(confirmed=False)
        
    def CEPE(self, year, user, confirmed=True):
        return self.get_candidates(
            year=year, exam=constants.EXAM_CEPE, 
            user=user, confirmed=confirmed
        )
    
    def BEPC(self, year, user, confirmed=True):
        return self.get_candidates(
            year=year, exam=constants.EXAM_BEPC, 
            user=user, confirmed=confirmed
        )
    
    def BAC(self, year, user, confirmed=True):
        return self.get_candidates(
            year=year, exam=constants.EXAM_BAC, 
            user=user, confirmed=confirmed
        )

    def favoured(self, year, user, exam=constants.EXAM_CEPE):
        min_avg = year.get_min_avg(exam)
        return self.get_candidates(year, user, exam)\
            .filter(Q(favoured=True) | (Q(average__gte=min_avg) & Q(average__lt=10)))
    
    def count_students(self, user, year, exam, confirmed=True):
        return self.get_candidates(
            user=user, year=year, exam=exam, confirmed=confirmed).count()


class Enrollment(model_utils.TimeStampedModel):
    school = models.ForeignKey(School, on_delete=models.PROTECT, 
        verbose_name=_('école'))
    exam = models.CharField(max_length=4, choices=constants.EXAM_CHOICES)
    exam_type = models.CharField(max_length=1, 
        choices=constants.EXAM_TYPES_CHOICES, 
        default=constants.EXAM_TYPE_OFFICIEL)
    student = models.ForeignKey(Student, on_delete=models.CASCADE, 
        verbose_name=_('élève'))
    year = models.ForeignKey(Year, on_delete=models.PROTECT, 
        verbose_name=_('année scolaire'))
    agent = models.ForeignKey(settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, verbose_name=_('inscrit par'))    
    active = models.BooleanField(_('actif'), 
        help_text=_("indique si l'élève est actif ou inactif. Un élève " + 
            "inactif n'apparaîtra pas dans la liste des candidats"), 
            default=True)
    confirmed = models.BooleanField(_('dossier validé'), 
        help_text="Indique si l'élève est un candidat confirmé", 
        default=False)
    center = models.ForeignKey('Center', on_delete=models.SET_NULL, null=True, blank=True)
    room = models.ForeignKey('Room', on_delete=models.SET_NULL, null=True, blank=True)
    table_num = models.CharField(max_length=8, null=True, blank=True)
    anonymat = models.CharField(max_length=8, null=True, blank=True)
    total = models.FloatField(default=0)
    average = models.FloatField(_('E. Nat.'), default=0)
    mock_average = models.FloatField(_('E. Blanc'), null=True, blank=True)
    gen_average = models.FloatField(_('MGA'), null=True, blank=True)
    rank = models.PositiveSmallIntegerField(default=0)
    favoured = models.BooleanField(default=False)
    ecolepro_id = models.BigIntegerField(null=True, blank=True)
    issues = models.ManyToManyField(Issue, blank=True)
    objects = models.Manager()
    candidates = CandidatesManager()

    def __str__(self):
        return self.student.__str__()

    class Meta:
        verbose_name = _('inscription')
        unique_together = [['student', 'year']]
        permissions = (
            ('change_student_status', 'can change student status'),
        )


class PaymentManager(models.Manager):
    def get_queryset(self, user=None, year=None):
        cached = cache.get('payments')
        if cached:
            return cached
        queryset = super().get_queryset()\
            .select_related('school', 'school__local_commission')
        
        if year:
            queryset = queryset.filter(year=year)
        if user and user.role != constants.ROLE_COMMISSION_NATIONALE:
            if user.role == constants.ROLE_ECOLE:
                return queryset.filter(school=user.school)
            elif user.role == constants.ROLE_COMMISSION_LOCALE:
                return queryset.filter(school__local_commission=user.localcommission)
        
        cache.set('payments', queryset, 60 * 60)
        return queryset
    
    def get_total(self, user, year):
        return self.get_queryset(user=user, year=year).aggregate(
            total=models.aggregates.Sum('amount'))['total']


class SchoolPayment(models.Model):
    amount = models.PositiveIntegerField(
        _('montant du versement'),
        validators=[MinValueValidator(1)])
    date = models.DateField(_('date'))
    school = models.ForeignKey(School, on_delete=models.CASCADE, 
        verbose_name=_('école'))
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    objects = PaymentManager()

    class Meta:
        verbose_name = _('versement')
        ordering = ['school__local_commission', 'school', '-date']

    def __str__(self):
        return f'{self.school} {self.amount} ({self.date})'


class SchoolFeesManager(models.Manager):
    def get_for_user(self, user, year=None):
        cached = cache.get('school_fees')
        if cached:
            return cached
        
        if not year:
            year = custom_utils.get_current_year()
        
        queryset = super().get_queryset().filter(year=year)\
            .select_related('school__local_commission')
        role = user.role
        if user.role == constants.ROLE_ECOLE:
            return queryset.filter(school=user.school)
        elif user.role == constants.ROLE_COMMISSION_LOCALE:
            queryset = queryset.filter(
                school__local_commission=user.localcommission)
        cache.set('school_fees', queryset, 60 * 60)
        return queryset
    
    def has_paid_year_fees(self, user, year=None):
        return self.get_for_user(user, year).exists()
    

class SchoolFees(models.Model):
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    agent = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
        null=True)
    amount = models.PositiveSmallIntegerField(_('montant'))
    date = models.DateTimeField(_('date'), auto_now_add=True)
    objects = SchoolFeesManager()

    class Meta:
        verbose_name = "droits d'examen"
        verbose_name_plural = "droits d'examen"
        unique_together = [
            ['year', 'school']
        ]


class CenterManager(models.Manager):
    def get_for_year(self, year=None, user=None, 
        exam=constants.EXAM_CEPE, annotate=True, 
        annotate_stats=False, annotate_results=False,
        for_correction=False):

        queryset = None
        if not year:
            year = custom_utils.get_current_year()
        
        cached = None
        if year == custom_utils.get_current_year():
            cached = cache.get('centers')

        if cached:
            queryset = cached
        else:
            queryset = super().get_queryset()
            cache.set('centers', queryset, 60 * 60)
        
        queryset = queryset.filter(exam=exam, year=year) \
            .select_related('school__local_commission', 'location')
        
        if user and user.role == constants.ROLE_COMMISSION_LOCALE:
            if not for_correction:
                queryset = queryset.filter(location=user.localcommission)
            else:
                queryset = queryset.filter(
                    Q(correction_center__correction_center=user.localcommission)
                )

        if annotate:
            queryset = queryset.annotate(
                candidates=Count('enrollment', distinct=True),
                rooms=Count('room', distinct=True),
                rooms_capacity=Subquery(
                    Room.objects.filter(
                        center=OuterRef('pk')
                    ).values('center').annotate(
                        total_capacity=Sum('capacity')
                    ).values('total_capacity')
                ),
                schools=Count('center_schools', distinct=True),
                # schools_students_count=Count('enrollment', 
                #     filter=Q(enrollment__center__pk=OuterRef('pk')) & \
                #         Q(enrollment__confirmed=True) & \
                #         Q(enrollment__year=year) & \
                #         Q(enrollment__exam=exam), 
                #     distinct=True),
                schools_students_count=Subquery(
                    Enrollment.objects.filter(
                        center__pk=OuterRef('pk'),
                        confirmed=True,
                        year=year,
                        exam=exam
                    ).values('id').annotate(
                        value=Count('id', distinct=True)
                    ).values('value')[:1]
                ),
            )

        elif annotate_stats:
            min_avg = year.get_min_avg(exam)
            queryset = queryset.annotate(
                candidates=Count('enrollment', distinct=True), 
                admitted=Count('enrollment', filter=Q(enrollment__gen_average__gte=10), distinct=True),
                admissible=Count(
                    'enrollment', 
                    filter=Q(enrollment__favoured=True) | (Q(enrollment__gen_average__lt=10) & Q(enrollment__gen_average__gte=min_avg)), 
                    distinct=True),
                failed=Count('enrollment', filter=Q(enrollment__gen_average__lt=min_avg), distinct=True),
                all_admitted=Count('enrollment', filter=Q(enrollment__gen_average__gte=min_avg) | Q(enrollment__favoured=True), distinct=True),
                present=Count('enrollment', filter=Q(enrollment__gen_average__gt=1), distinct=True),
                missing=Count('enrollment', filter=Q(enrollment__gen_average__lte=1), distinct=True),
            )
        elif annotate_results:
            min_avg = year.get_min_avg(exam)
            queryset = queryset.select_related('school').annotate(
                boys_count=Count('enrollment', filter=Q(enrollment__student__gender=constants.GENDER_MALE), distinct=True), 
                girls_count=Count('enrollment', filter=Q(enrollment__student__gender=constants.GENDER_FEMALE), distinct=True), 
                students_count=Count('enrollment', distinct=True), 
                boys_present=Count(
                    'enrollment', 
                    filter=Q(enrollment__student__gender=constants.GENDER_MALE) & Q(enrollment__total__gt=1), 
                    distinct=True), 
                girls_present=Count(
                    'enrollment', 
                    filter=Q(enrollment__student__gender=constants.GENDER_FEMALE) & Q(enrollment__total__gt=1), 
                    distinct=True), 
                students_present=Count(
                    'enrollment', 
                    filter=Q(enrollment__total__gt=1), distinct=True), 
                boys_admitted=Count(
                    'enrollment', 
                    filter=Q(enrollment__student__gender=constants.GENDER_MALE) & Q(enrollment__gen_average__gte=min_avg), 
                    distinct=True), 
                girls_admitted=Count(
                    'enrollment', 
                    filter=Q(enrollment__student__gender=constants.GENDER_FEMALE) & Q(enrollment__gen_average__gte=min_avg), 
                    distinct=True), 
                students_admitted=Count(
                    'enrollment', 
                    filter=Q(enrollment__gen_average__gte=min_avg), distinct=True), 
                boys_perc=Count(
                    'enrollment', 
                    filter=Q(enrollment__student__gender=constants.GENDER_MALE) & Q(enrollment__gen_average__gte=min_avg), 
                    distinct=True) / Count(
                    'enrollment', 
                    filter=Q(enrollment__student__gender=constants.GENDER_MALE) & Q(enrollment__gen_average__gt=1), 
                    distinct=True) * 100.0, 
                girls_perc=Count(
                    'enrollment', 
                    filter=Q(enrollment__student__gender=constants.GENDER_FEMALE) & Q(enrollment__gen_average__gte=min_avg), 
                    distinct=True) / Count(
                    'enrollment', 
                    filter=Q(enrollment__student__gender=constants.GENDER_FEMALE) & Q(enrollment__total__gt=1), 
                    distinct=True) * 100.0, 
                students_perc=Count(
                    'enrollment', 
                    filter=Q(enrollment__gen_average__gte=min_avg), 
                    distinct=True) / Count(
                    'enrollment', 
                    filter=Q(enrollment__total__gt=1), 
                    distinct=True) * 100.0, 
            )
        return queryset


class Center(models.Model):
    exam = models.CharField(max_length=4, choices=constants.EXAM_CHOICES, 
        default=constants.EXAM_CEPE)
    year = models.ForeignKey(Year, on_delete=models.CASCADE, null=True)
    location = models.ForeignKey(LocalCommission, on_delete=models.CASCADE, null=True)
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    identifier = models.CharField(max_length=255, null=True, blank=True)
    director = models.CharField(max_length=255)
    phone = models.CharField(max_length=255)
    correction_center = models.ForeignKey(
        'CorrectionCenter', null=True, blank=True,
        on_delete=models.SET_NULL)
    complete = models.BooleanField(default=False)
    comment = models.CharField(max_length=255, null=True, blank=True)
    objects = CenterManager()

    class Meta:
        verbose_name = 'centre'
        unique_together = ['year', 'exam', 'school']
        unique_together = ['year', 'exam', 'location', 'identifier']

    def __str__(self):
        center = self.identifier or f'{self.school}'
        return str(center.upper())
    
    def rooms_count(self):
        return self.room_set.aggregate(total=Sum('capacity'))['total'] or 0

    def count_candidates_for_exam(self, exam):
        enrollments_count = 0
        year = custom_utils.get_current_year()
        for school in self.center_schools.all():
            enrollments_count += school.enrollment_set.filter(
                exam=exam, confirmed=True, year=year).count()
        return enrollments_count
    
    def count_cepe_candidates(self):
        return self.count_candidates_for_exam(constants.EXAM_CEPE)
    
    def count_bepc_candidates(self):
        return self.count_candidates_for_exam(constants.EXAM_BEPC)
    
    def count_bac_candidates(self):
        return self.count_candidates_for_exam(constants.EXAM_BAC)
    
class RoomManager(models.Manager):
    def get_for_year(self, user=None, year=None, exam=constants.EXAM_CEPE, with_exam=True):
        queryset = None
        if not year:
            year = custom_utils.get_current_year()
        if year == custom_utils.get_current_year() and cache.get('rooms'):
            queryset = cache.get('rooms')
        else:
            queryset = super().get_queryset().filter(center__year=year)
            cache.set('rooms', queryset, 60 * 5)

        if with_exam:
            queryset = queryset.filter(center__exam=exam)
        queryset = queryset.select_related('center__school__local_commission')
        if user and user.role == constants.ROLE_COMMISSION_LOCALE:
            return queryset.filter(
                center__school__local_commission=user.localcommission, 
                center__year=year)
        queryset = queryset.annotate(candidates_count=Count('enrollment'))
        return queryset.order_by('center__school__local_commission', 'center__school', 'number')
    

class Room(models.Model): 
    center = models.ForeignKey(Center, on_delete=models.CASCADE)
    number = models.PositiveSmallIntegerField()
    capacity = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1)])
    objects = RoomManager()
    
    def __str__(self):
        return f'Salle {self.number}'
    
    class Meta:
        verbose_name = 'salle'
        unique_together = [['center', 'number'],]


class MonitorManager(models.Manager):
    def get_for_year(self, year, user=None):
        queryset = super().get_queryset()
        if user and user.role == constants.ROLE_COMMISSION_LOCALE:
            return queryset.filter(
                year=year,
                center__school__local_commission=user.localcommission)
        return queryset


class Monitor(models.Model):
    center = models.ForeignKey(Center, on_delete=models.CASCADE)
    full_name = models.CharField(max_length=255)
    full_name_ar = models.CharField(max_length=255)
    phone = models.CharField(max_length=10)
    objects = MonitorManager()

    def __str__(self):
        return f'{self.full_name}'

    class Meta:
        verbose_name = 'surveillant'


class SubjectManager(models.Manager):
    def get_queryset(self, exam=None, year=None, location=None):
        qs = super().get_queryset()

        if year:
            qs = qs.filter(year=year)
        if exam:
            qs = qs.filter(exam=exam)
        if location and qs.filter(location=location).exists():
            qs = qs.filter(location=location)
        return qs


class Subject(models.Model):
    SUBJECT_GROUP_ARABIC = 'A'
    SUBJECT_GROUP_RELIGION = 'R'
    SUBJECT_GROUP_CHOICES = (
        (SUBJECT_GROUP_RELIGION, 'Religion'),
        (SUBJECT_GROUP_ARABIC, 'Arabe'),
    )
    name = models.CharField(max_length=255)
    translation = models.CharField(max_length=255)
    year = models.ForeignKey(Year, on_delete=models.CASCADE, null=True)
    coefficient = models.PositiveSmallIntegerField(default=1, 
        validators=[MinValueValidator(1)])
    active = models.BooleanField(default=True)
    exam = models.CharField(max_length=4, choices=constants.EXAM_CHOICES, 
        default=constants.EXAM_CEPE)
    group = models.CharField(max_length=1, 
        choices=SUBJECT_GROUP_CHOICES, default=SUBJECT_GROUP_RELIGION)
    location = models.ForeignKey(LocalCommission, on_delete=models.CASCADE, null=True)
    order = models.PositiveSmallIntegerField(default=0)
    objects = SubjectManager()

    class Meta:
        verbose_name = 'matière'
        unique_together = [
            ['year', 'name', 'exam'],
        ]
        ordering = ['order']
    
    def __str__(self):
        return self.name
    
    def get_abbr(self):
        return self.name[:4].upper()


class GradeManager(models.Manager):
    def get_for_year(self, year, exam=None):
        queryset = super().get_queryset().filter(enrollment__year=year)
        if exam: 
            queryset = queryset.filter(enrollment__exam=exam)
        return queryset.select_related('enrollment', 'enrollment__student')


class Grade(models.Model):
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE)
    enrollment = models.ForeignKey(Enrollment, on_delete=models.CASCADE)
    value = models.PositiveSmallIntegerField(default=0)
    objects = GradeManager()

    class Meta:
        verbose_name = 'note'
        unique_together = [
            ['subject', 'enrollment']
        ]
        permissions = (
            ('view_results', 'can view exams results'),
            ('view_mock_results', 'can view mock exams results'),
            ('edit_mock_exam_grade', 'can edit mock exam grade'),
        )
    
    def __str__(self):
        return f'{self.enrollment} -> {self.subject} -> ({self.value})'


class Distinction(models.Model):
    average = models.FloatField(max_length=255, default=0, unique=True)
    name = models.CharField(max_length=255)
    translation = models.CharField(max_length=255)
    short_name = models.CharField(max_length=10)

    class Meta:
        verbose_name = 'mention'
        ordering = ['average']

    def __str__(self):
        return f'{self.name} ({self.average})'


class CorrectionManager(models.Manager):
    def get_for_year(self, user, year, exam=constants.EXAM_CEPE):
        queryset = super().get_queryset().filter(year=year, exam=exam)

        if user.role == constants.ROLE_ECOLE:
            queryset = queryset.filter(school=user.school)
        elif user.role == constants.ROLE_COMMISSION_LOCALE:
            queryset = queryset.filter(
                school__local_commission=user.localcommission)
        return queryset        

class StudentCorrection(models.Model):
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    school = models.ForeignKey(School, on_delete=models.CASCADE, null=True)
    table_num = models.CharField(max_length=6)
    exam = models.CharField(max_length=4, choices=constants.EXAM_CHOICES, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.BooleanField(default=False)
    commission_confirmation = models.BooleanField(default=False)
    student = models.OneToOneField(Student, on_delete=models.CASCADE, null=True)

    # Initial data
    identifier = models.CharField(max_length=12, null=True, blank=True)
    initial_last_name_fr = models.CharField(max_length=255)
    initial_first_name_fr = models.CharField(max_length=255)
    initial_gender = models.CharField(max_length=255, choices=constants.GENDER_CHOICES)
    initial_full_name_ar = models.CharField(max_length=255)
    initial_birth_date = models.DateField()
    initial_birth_place = models.CharField(max_length=255)
    initial_birth_place_ar = models.CharField(max_length=255)
    initial_nationality = models.CharField(
        max_length=3, choices=constants.NATIONALITY_CHOICES)
    initial_exam_type = models.CharField(
        max_length=3, choices=constants.EXAM_TYPES_CHOICES)
    
    # Modifications requested
    new_last_name_fr = models.CharField(max_length=255, null=True, blank=True)
    new_first_name_fr = models.CharField(max_length=255, null=True, blank=True)
    new_full_name_ar = models.CharField(max_length=255, null=True, blank=True)
    new_gender = models.CharField(max_length=255, choices=constants.GENDER_CHOICES, 
        null=True, blank=True)
    new_birth_date = models.DateField(null=True, blank=True)
    new_birth_place = models.CharField(max_length=255, null=True, blank=True)
    new_birth_place_ar = models.CharField(max_length=255, 
        null=True, blank=True)
    new_nationality = models.CharField(
        max_length=3, choices=constants.NATIONALITY_CHOICES, 
        null=True, blank=True)
    new_exam_type = models.CharField(
        max_length=3, choices=constants.EXAM_TYPES_CHOICES, 
        null=True, blank=True)
    new_photo = CloudinaryField(
        'image', folder='cherifla/correction_photos/', 
        null=True, blank=True)
    new_certificate = CloudinaryField(
        'image', folder='cherifla/correction_certificates/', 
        null=True, blank=True)
    objects = CorrectionManager()

    def __str__(self):
        full_name = f'{self.new_last_name_fr} {self.new_first_name_fr}'
        return f'Correction pour {full_name}'

    class Meta:
        verbose_name = 'demande de correction'
        verbose_name_plural = 'demandes de correction'
        permissions = (
            ('change_correction_status', 'can change correction status'),
        )
        ordering = ['status', 'school', 'initial_last_name_fr', 'initial_first_name_fr']


class StudentCard(model_utils.TimeStampedModel):
    LEVEL_CP1 = 'CP1'
    LEVEL_CP2 = 'CP2'
    LEVEL_CE1 = 'CE1'
    LEVEL_CE2 = 'CE2'
    LEVEL_CM1 = 'CM1'
    LEVEL_CM2 = 'CM2'
    LEVEL_6EME = '6EME'
    LEVEL_5EME = '5EME'
    LEVEL_4EME = '4EME'
    LEVEL_3EME = '3EME'
    LEVEL_2NDE = '2NDE'
    LEVEL_1ERE = '1ERE'
    LEVEL_TLE = 'TLE'

    LEVEL_CHOICES = (
        (LEVEL_CP1, 'CP1'),
        (LEVEL_CP2, 'CP2'),
        (LEVEL_CE1, 'CE1'),
        (LEVEL_CE2, 'CE2'),
        (LEVEL_CM1, 'CM1'),
        (LEVEL_CM2, 'CM2'),
        (LEVEL_6EME, '6EME'),
        (LEVEL_5EME, '5EME'),
        (LEVEL_4EME, '4EME'),
        (LEVEL_3EME, '3EME'),
        (LEVEL_2NDE, '2NDE'),
        (LEVEL_1ERE, '1ERE'),
        (LEVEL_TLE, 'TLE'),
    )

    STATUS_PENDING = 'P'
    STATUS_MANUFACTURED = 'M'
    STATUS_SHIPPED = 'S'
    STATUS_CHOICES = (
        (STATUS_PENDING, 'EN COURS'),
        (STATUS_MANUFACTURED, 'IMPRIMEE'),
        (STATUS_SHIPPED, 'LIVREE'),
    )
    enrollment = models.OneToOneField(Enrollment, null=True, blank=True, 
                                      on_delete=models.SET_NULL)
    year = models.ForeignKey(Year, on_delete=models.CASCADE)
    matricule_dsps = models.CharField(_('matricule DSPS'), max_length=20, null=True, blank=True)
    matricule_cherifla = models.CharField(max_length=20, null=True, blank=True)
    last_name = models.CharField(_('nom'), max_length=255)
    first_name = models.CharField(_('prénoms'), max_length=255)
    gender = models.CharField(_('sexe'), max_length=255, choices=constants.GENDER_CHOICES)
    birth_date = models.DateField(_('date de naissance'))
    birth_place = models.CharField(_('lieu de naissance'), max_length=255)
    phone = models.CharField(_("contact d'urgence"), max_length=20)
    level = models.CharField(_('niveau'), max_length=10, choices=LEVEL_CHOICES)
    status = models.CharField(_('statut'), max_length=1, choices=STATUS_CHOICES, 
                              default=STATUS_PENDING)
    card_payment_status = models.CharField(max_length=2, 
        choices=constants.CARD_STATUS_CHOICES,
        default=constants.CARD_STATUS_UNPAID)
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    photo = CloudinaryField(
        'image', folder='cherifla/students/id_card/', 
        null=True,
        transformation={
            **CLOUDINARY_TRANFORMATIONS
        })
    comment = models.CharField(max_length=255, null=True, blank=True)
    class Meta:
        verbose_name = _('carte scolaire')
        verbose_name_plural = _('cartes scolaires')
        permissions = (
            ('convert_studentcard', 'Can convert student card into candidate'),
        )

    def get_school_drena(self):
        return str(self.school.drena_obj) or self.school.drena 

    def __str__(self):
        return f'{self.last_name} {self.first_name}'

    def photo_name(self):
        if self.photo:
            return str(self.photo.url).split('/')[-1]

    def matricule_or_id(self):
        return self.matricule_dsps or self.matricule_cherifla

    def birth_date_str(self):
        return self.birth_date.strftime('%d/%m/%Y')
    
    def created_at_str(self):
        return self.date_created.strftime('%d/%m/%Y')

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # Generate identifier
        if not self.matricule_cherifla:
            self.matricule_cherifla = f"E{self.year.short_name[2:]}{str(self.id).zfill(4)}"
        return super().save(update_fields=['matricule_cherifla'])

    def enrollment_identifier(self):
        if self.enrollment:
            return self.enrollment.student.identifier
        return ''

class TransfertManager(models.Manager):
    def for_user(self, user, year):
        queryset = self.get_queryset().filter(enrollment__year=year)
        if user.role == constants.ROLE_ECOLE:
            queryset = queryset.filter(enrollment__school=user.school)
        elif user.role == constants.ROLE_COMMISSION_LOCALE:
            queryset = queryset.filter(enrollment__school__local_commission=user.localcommission)
        return queryset.select_related('enrollment__student', 'old_school', 'school')


class TransferRequest(model_utils.TimeStampedModel):
    enrollment = models.ForeignKey(Enrollment, on_delete=models.CASCADE, verbose_name="élève")
    old_school = models.ForeignKey(School, on_delete=models.CASCADE, verbose_name="école d'origine")
    school = models.ForeignKey(School, on_delete=models.CASCADE, 
                                   verbose_name="école d'accueil", related_name='new_schools')
    confirmed = models.BooleanField('statut', default=False)
    objects = TransfertManager()

    class Meta:
        verbose_name = 'demande de transfert'
        verbose_name_plural = 'demandes de transfert'
        permissions = (
            ('confirm_transfert', 'can mark transfert as confirmed',),
        )

    def __str__(self):
        return f'Demande de transfert {self.enrollment} de {self.old_school} vers {self.school}'
    

class CorrectionCenter(models.Model):
    year = models.ForeignKey(Year, on_delete=models.CASCADE, null=True)
    exam = models.CharField(
        max_length=4, choices=constants.EXAM_CHOICES, 
        default=constants.EXAM_CEPE)
    correction_center = models.ForeignKey(
        LocalCommission, on_delete=models.CASCADE,
        verbose_name=_('lieu de correction'))
    locations = models.ManyToManyField(LocalCommission, related_name='correction_centers')

    class Meta:
        verbose_name = _('centre de correction')
        verbose_name_plural = _('centres de correction')
        unique_together = [
            ['year', 'exam', 'correction_center']
        ]

    def __str__(self):
        return f'{self.correction_center} | {self.exam}'.upper()


class Location(models.Model):
    name = models.CharField('DRENA', max_length=255, unique=True)

    class Meta:
        verbose_name = 'DRENA'
        verbose_name_plural = 'DRENA'
        ordering = ['name']

    def __str__(self):
        return self.name


class Staff(model_utils.TimeStampedModel):
    STATUS_PENDING = 'P'
    STATUS_MANUFACTURED = 'M'
    STATUS_SHIPPED = 'S'
    STATUS_CHOICES = (
        (STATUS_PENDING, 'EN COURS'),
        (STATUS_MANUFACTURED, 'IMPRIMEE'),
        (STATUS_SHIPPED, 'LIVREE'),
    )

    ROLE_TEACHER = 'E'
    ROLE_ACCOUNTANT = 'C'
    ROLE_DIRECTOR = 'D'
    ROLE_WATCHMAN = 'G'
    ROLE_FOUNDER = 'F'
    ROLE_SECRETARY = 'S'
    ROLE_CHOICES = (
        (ROLE_TEACHER, 'Enseignant'),
        (ROLE_ACCOUNTANT, 'Comptable'),
        (ROLE_DIRECTOR, 'Directeur'),
        (ROLE_SECRETARY, 'Sécrétaire'),
        (ROLE_WATCHMAN, 'Gardien'),
        (ROLE_FOUNDER, 'Fondateur'),
    )

    code = models.CharField('matricule', max_length=4)
    last_name = models.CharField('nom', max_length=255)
    first_name = models.CharField('prénoms', max_length=255)
    birth_date = models.DateField(_('date de naissance'))
    birth_place = models.CharField(_('lieu de naissance'), max_length=255)
    address = models.CharField(_('lieu de résidence'), max_length=255, null=True)
    gender = models.CharField(
        _('sexe'), max_length=255, 
        choices=constants.GENDER_CHOICES,
        default=constants.GENDER_MALE
    )
    father = models.CharField('père', max_length=255)
    mother = models.CharField('mère', max_length=255)
    phone = models.CharField('contact', max_length=255)
    date_hired = models.DateField("date d'embauche", null=True, blank=True)
    location = models.ForeignKey(Location, on_delete=models.CASCADE, verbose_name='DRENA')
    school = models.ForeignKey(School, on_delete=models.CASCADE, verbose_name='école')
    education = models.CharField(_("type d'enseignement"), max_length=1, 
                                 choices=constants.EDUCATION_CHOICES)
    job = models.CharField(
        'emploi/fonction', max_length=1, choices=ROLE_CHOICES)
    agent = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
        null=True, verbose_name='inscrit par')
    photo = CloudinaryField(
        'photo', folder='cherifla/staff/', 
        transformation={
            **CLOUDINARY_TRANFORMATIONS
        }, null=True, blank=True
    )
    year = models.ForeignKey(Year, on_delete=models.CASCADE, null=True, blank=True)
    status = models.CharField(_('statut'), max_length=1, choices=STATUS_CHOICES, 
                              default=STATUS_PENDING)
    card_payment_status = models.CharField(max_length=2, 
        choices=constants.CARD_STATUS_CHOICES,
        default=constants.CARD_STATUS_UNPAID)
    issues = models.ManyToManyField(Issue, blank=True)
    class Meta:
        verbose_name = 'Enseignant'
        verbose_name_plural = 'Enseignant'

    def __str__(self):
        return f'{self.last_name} {self.first_name}'



class SchoolStatistics(model_utils.TimeStampedModel):
    """School statistics"""
    school = models.ForeignKey(School, on_delete=models.PROTECT)
    year = models.ForeignKey(Year, on_delete=models.PROTECT)
    levels_count = models.PositiveSmallIntegerField(_("nombre de classes"), 
        null=True, blank=False, validators=[MinValueValidator(1)])
    students_count = models.PositiveSmallIntegerField(_("nombre total d'élèves"), 
        null=True, blank=False, validators=[MinValueValidator(1)])
    boys_count = models.PositiveSmallIntegerField(_("nombre de garçons"), 
        null=True, blank=False, validators=[MinValueValidator(1)])
    girls_count = models.PositiveSmallIntegerField(_("nombre de filles"), 
        null=True, blank=False, validators=[MinValueValidator(1)])
    stats_completed = models.BooleanField(default=False)

    class Meta:
        verbose_name = "Statistiques d'école"
        verbose_name_plural = "Statistiques d'écoles"
        unique_together = ('school', 'year')

    # def save(self, *args, **kwargs):
    #     self.students_count = self.boys_count + self.girls_count
    #     super().save(*args, **kwargs)