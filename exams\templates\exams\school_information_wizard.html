{% extends 'base_minimal.html' %}
{% load static widget_tweaks %}

{% block title %}{{ step_title }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .progress {
        height: 10px;
        border-radius: 10px;
        background: linear-gradient(90deg, #e3f2fd 0%, #bbdefb 100%);
    }

    .progress-bar {
        background: linear-gradient(90deg, #1976d2 0%, #0d47a1 100%);
        border-radius: 10px;
        transition: width 0.6s ease;
    }

    .form-control:focus {
        border-color: #1976d2;
        box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
    }

    .badge-primary {
        background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    }

    .btn-outline-danger {
        color: white;
        border-color: rgba(255, 255, 255, 0.5);
    }

    .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    .logo-img {
        height: 50px;
        width: auto;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    @media (max-width: 768px) {
        .logo-img {
            height: 40px;
        }
    }
</style>
{% endblock %}

{% block content %}
{% with WIDGET_ERROR_CLASS='is-invalid' %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header with Logo and Logout -->
            <div class="d-flex justify-content-between align-items-center mb-4 mt-3">
                <div class="d-flex align-items-center">
                    <img src="{% static 'images/logo.jpg' %}" alt="Logo" class="logo-img me-3">
                </div>
                <div>
                    <a href="{% url 'logout' %}" class="btn btn-outline-danger btn-sm">
                        <i data-feather="log-out" class="me-1"></i>
                        Déconnexion
                    </a>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body py-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="mb-0">Mise à jour des informations</h5>
                        <span class="badge badge-primary">Étape {{ step_index|add:1 }} sur {{ step_count }}</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-primary progress-bar-animated"
                             role="progressbar"
                             style="width: {{ progress_percentage }}%"
                             aria-valuenow="{{ progress_percentage }}"
                             aria-valuemin="0"
                             aria-valuemax="100">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Form Card -->
            <div class="card shadow-lg border-0 rounded-lg">
                <div class="card-header bg-primary text-white py-4">
                    <h3 class="mb-0 text-center">
                        {% if current_step == 'school_info' %}
                            <i data-feather="school" class="me-2"></i>
                        {% else %}
                            <i data-feather="bar-chart-2" class="me-2"></i>
                        {% endif %}
                        {{ step_title }}
                    </h3>
                </div>
                <div class="card-body p-5">
                    <form method="post">
                        {% csrf_token %}
                        {{ wizard.management_form }}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <div class="form-row">
                            {% if current_step == 'school_info' %}
                                <!-- School Information Step -->
                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.school_type.id_for_label }}">Type d'école (ECC/ESIA) <span class="text-danger">*</span></label>
                                    {% render_field form.school_type class='form-control' %}
                                </div>

                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.school_cycle.id_for_label }}">Cycle de l'école <span class="text-danger">*</span></label>
                                    {% render_field form.school_cycle class='form-control' %}
                                </div>

                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.school_status.id_for_label }}">Statut de l'école (Reconnu/Non rec.) <span class="text-danger">*</span></label>
                                    {% render_field form.school_status class='form-control' %}
                                </div>

                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.name.id_for_label }}">Nom de l'école (Français) <span class="text-danger">*</span></label>
                                    {% render_field form.name class='form-control' %}
                                </div>

                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.name_ar.id_for_label }}">Nom de l'école (Arabe)</label>
                                    {% render_field form.name_ar class='form-control' %}
                                </div>

                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.drena_obj.id_for_label }}">DRENA <span class="text-danger">*</span></label>
                                    {% render_field form.drena_obj class='form-control' %}
                                </div>

                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.iepp.id_for_label }}">IEPP</label>
                                    {% render_field form.iepp class='form-control' %}
                                </div>

                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.teachers_count.id_for_label }}">Nombre d'enseignants <span class="text-danger">*</span></label>
                                    {% render_field form.teachers_count class='form-control' %}
                                </div>

                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.director.id_for_label }}">Directeur <span class="text-danger">*</span></label>
                                    {% render_field form.director class='form-control' %}
                                </div>

                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.phone.id_for_label }}">Contact <span class="text-danger">*</span></label>
                                    {% render_field form.phone class='form-control' %}
                                    <small class="form-text text-muted">de préférence, un numéro whatsapp</small>
                                </div>

                            {% elif current_step == 'school_stats' %}
                                <!-- School Statistics Step -->
                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.levels_count.id_for_label }}">Nombre de classes <span class="text-danger">*</span></label>
                                    {% render_field form.levels_count class='form-control' %}
                                </div>

                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.boys_count.id_for_label }}">Nombre de garçons <span class="text-danger">*</span></label>
                                    {% render_field form.boys_count class='form-control' onchange='updateStudentCount()' %}
                                </div>

                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.girls_count.id_for_label }}">Nombre de filles <span class="text-danger">*</span></label>
                                    {% render_field form.girls_count class='form-control' onchange='updateStudentCount()' %}
                                </div>
                                <div class="form-group mb-2 col-md-6">
                                    <label class="text-muted" for="{{ form.students_count.id_for_label }}">Nombre total d'élèves <span class="text-danger">*</span></label>
                                    {% render_field form.students_count class='form-control' %}
                                </div>
                            {% endif %}

                        </div>

                        <!-- Navigation Buttons -->
                        <div class="d-flex justify-content-between mt-4 pt-3 border-top">
                            {% if wizard.steps.prev %}
                                <button type="submit" name="wizard_goto_step" value="{{ wizard.steps.prev }}"
                                        class="btn btn-outline-secondary btn-lg px-4" formnovalidate="formnovalidate">
                                    <i data-feather="arrow-left" class="me-2"></i>
                                    Précédent
                                </button>
                            {% else %}
                                <a href="{% url 'school_information_check' %}" class="btn btn-outline-secondary btn-lg px-4">
                                    <i data-feather="arrow-left" class="me-2"></i>
                                    Retour
                                </a>
                            {% endif %}

                            {% if wizard.steps.next %}
                                <button type="submit" class="btn btn-primary btn-lg px-4">
                                    Suivant
                                    <i data-feather="arrow-right" class="ms-2"></i>
                                </button>
                            {% else %}
                                <button type="submit" class="btn btn-success btn-lg px-4">
                                    <i data-feather="check" class="me-2"></i>
                                    Terminer
                                </button>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.progress-bar {
    transition: width 0.6s ease;
}

.card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: slideInUp 0.6s ease;
}
</style>

<script>

function updateStudentCount() {
    const boys = parseInt(document.querySelector('[name="boys_count"]').value) || 0;
    const girls = parseInt(document.querySelector('[name="girls_count"]').value) || 0;
    const total = boys + girls;

    const studentCountField = document.querySelector('[name="students_count"]');
    if (studentCountField) {
        studentCountField.value = total;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Auto-calculate total students
    const studentsCount = document.getElementById('id_students_count');
    const boysCount = document.getElementById('id_boys_count');
    const girlsCount = document.getElementById('id_girls_count');

    if (studentsCount && boysCount && girlsCount) {
        function updateTotal() {
            const boys = parseInt(boysCount.value) || 0;
            const girls = parseInt(girlsCount.value) || 0;
            studentsCount.value = boys + girls;
        }

        boysCount.addEventListener('input', updateTotal);
        girlsCount.addEventListener('input', updateTotal);
    }

    // Initialize feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
});
</script>
{% endwith %}
{% endblock %}
