{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Ecoles</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      LISTE DES ECOLES ET STATUT DE PAIEMENT DES COTISATIONS ANNUELLES
      {% if user.role == ROLE_COMMISSION_LOCALE %} DE {{ user.localcommission }} {% endif %}
      (Total payé: {{ payments_total|intcomma }} F)
    </h5>
  </div>

  {% if perms.exams.add_schoolfees %}
    <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
      <div>
        <a class="btn btn-success text-white"
           hx-get="{% url 'school_fees_add' %}"
           hx-target="#dialog">
           + Nouvelle cotisation</a>
      </div>

      <div class="dropdown">
        <button class="btn btn-warning dropdown" type="button"
                data-toggle="dropdown" aria-expanded="false">
          <i data-feather="file-text"></i> Imprimer
        </button>
        <div class="dropdown-menu">
            <a class="dropdown-item"
              href="{% url 'fees_pdf' %}">Liste cotisations </a>
          </div>
      </div>
    </div>
  {% endif %}

  <!-- Search and pagination controls -->
  <div class="form-group row">
    <div class="col-4">
      <label for="search" class="pl-2">Rechercher:</label>
      <input type="search" name="search" id="search"
              class="form-control btn-sm ml-2"
              value="{{ search }}"
              hx-get="{{ request.path }}"
              hx-target="#main-content"
              hx-include="[name=per_page]">
    </div>
    <div class="col-4">
      <label for="per_page" class="pl-2">Afficher</label>
      <select name="per_page" id="per_page" class="form-control form-control-sm"
              hx-get="{{ request.path }}?page={{ page }}"
              hx-target="#main-content"
              hx-include="[name=search]">
        <option value="10" {% if per_page|floatformat:'0' == '10' %} selected="selected" {% endif %}>10 éléments</option>
        <option value="25" {% if per_page|floatformat:'0' == '25' %} selected="selected" {% endif %}>25 éléments</option>
        <option value="50" {% if per_page|floatformat:'0' == '50' %} selected="selected" {% endif %}>50 éléments</option>
        <option value="100" {% if per_page|floatformat:'0' == '100' %} selected="selected" {% endif %}>100 éléments</option>
      </select>
    </div>
  </div>

  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0">
            <thead class="bg-secondary">
              <tr>
                {% if user.role != ROLE_ECOLE %}
                  <th scope="col" class="text-white">LOCALITE</th>
                {% endif %}
                <th scope="col" class="text-white">ECOLE</th>
                <th scope="col" class="text-white">TYPE D'ECOLE</th>
                <th scope="col" class="text-white">MONTANT A PAYER</th>
                <th scope="col" class="text-white">STATUT PAIEMENT</th>
                <th scope="col" class="text-white">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {% for school in schools %}
              <tr>
                {% if user.role != ROLE_ECOLE %}
                <td>{{ school.local_commission|default:'-' }}</td>
                {% endif %}
                <td>{{ school.name }}</td>
                <td>
                  {% if school.school_type %}
                    <span class="badge badge-pill badge-info">{{ school.school_type }}</span>
                  {% else %}
                    <span class="text-muted">-</span>
                  {% endif %}
                </td>
                <td>{% if school.school_type == 'ECC' %} {{ ecc_amount }} {% else %} {{ esia_amount }} {% endif %}  </td>
                <td>
                  {% if school.has_paid %}
                    <span class="badge badge-pill badge-success">
                      <i data-feather="check" class="feather-16 align-middle mr-1"></i>
                      Payé
                    </span>
                  {% else %}
                    <span class="badge badge-pill badge-warning">
                      <i data-feather="x" class="feather-16 align-middle mr-1"></i>
                      Non payé
                    </span>
                  {% endif %}
                </td>
                <td>
                  <div class="btn-group" role="group">
                    {% if not school.has_paid and perms.exams.add_schoolfees %}
                      <button class="btn btn-success btn-sm"
                              hx-get="{% url 'school_fees_confirm' school.id %}"
                              hx-target="#dialog"
                              title="Confirmer le paiement">
                        <i data-feather="check" class="feather-16 align-middle"></i>
                      </button>
                    {% endif %}

                    {% if school.has_paid %}
                      <button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="false">
                        <i data-feather="more-horizontal"></i>
                      </button>
                      <div class="dropdown-menu">
                        {% if perms.exams.change_schoolfees and school.fees_id %}
                        <a href="" class="dropdown-item btn btn-sm"
                           hx-get="{% url 'school_fees_edit' school.fees_id %}"
                           hx-target="#dialog">
                          <span data-feather="edit"></span>Modifier
                        </a>
                        {% endif %}
                      </div>
                    {% endif %}
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
          {% include 'components/pagination.html' with include_items="[name=per_page], [name=search]" %}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>