{% load widget_tweaks %}

<form action="{% url 'statistics_edit' statistics.id %}" method="post" class="modal-content" id="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">Effectifs - {{ statistics.school.name|upper }}</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-md-12 mb-3">
                <div class="alert alert-info">
                    <i data-feather="info"></i>
                    <strong>École:</strong> {{ statistics.school.name|upper }}<br>
                    <strong>Directeur:</strong> {{ statistics.school.director|upper }}<br>
                    <strong>Année:</strong> {{ statistics.year.short_name }}
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="{{ form.levels_count.id_for_label }}">Nombre de classes</label>
                {% render_field form.levels_count class='form-control' %}
                {% if form.levels_count.errors %}
                    <div class="text-danger small">{{ form.levels_count.errors.0 }}</div>
                {% endif %}
            </div>
            
            <div class="col-md-6 mb-3">
                <label for="{{ form.students_count.id_for_label }}">Nombre total d'élèves</label>
                {% render_field form.students_count class='form-control' %}
                {% if form.students_count.errors %}
                    <div class="text-danger small">{{ form.students_count.errors.0 }}</div>
                {% endif %}
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="{{ form.boys_count.id_for_label }}">Nombre de garçons</label>
                {% render_field form.boys_count class='form-control' %}
                {% if form.boys_count.errors %}
                    <div class="text-danger small">{{ form.boys_count.errors.0 }}</div>
                {% endif %}
            </div>
            
            <div class="col-md-6 mb-3">
                <label for="{{ form.girls_count.id_for_label }}">Nombre de filles</label>
                {% render_field form.girls_count class='form-control' %}
                {% if form.girls_count.errors %}
                    <div class="text-danger small">{{ form.girls_count.errors.0 }}</div>
                {% endif %}
            </div>
        </div>

        {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {{ form.non_field_errors }}
            </div>
        {% endif %}
    </div>
    
    <div class="modal-footer">
        <button type="submit" class="btn btn-success">Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>
</form>

<script>
if (typeof(feather) !== 'undefined') {
    feather.replace();
}
</script>
