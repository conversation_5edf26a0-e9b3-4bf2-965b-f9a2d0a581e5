{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item"><a href="#">Accueil</a></li>
      <li class="breadcrumb-item active" aria-current="page">Statistiques {{ selected_year }}</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h3 class="fw-bold">Effectifs déclarés {{ active_year }}
    </h3>
  </div>

  <div class="d-flex justify-content-between row mb-2 {% if user.role == ROLE_ECOLE %}d-none{% endif %}">
    <div class="wrapper col-6 {% if not user.role == ROLE_COMMISSION_NATIONALE %} d-none {% endif %}">
      Localité:
      <select name="locations_select" id="locations_select" 
        class="form-control" hx-get="{{ request.path }}" 
        hx-target="#main-content">
        <option value="0" {% if not selected_location %} selected="selected" {% endif %}>Sélectionner</option>
        {% for location in locations_list %}
          <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
        {% empty %}
        {% endfor %}
      </select>
    </div>
    
    <div class="wrapper col-6 {% if user.role == ROLE_ECOLE %} d-none {% endif %}">
      Ecole:
      <select name="schools_select" id="schools_select" 
      class="form-control"
      hx-get="{{ request.path }}" hx-target="#main-content" hx-include="[name=locations_select]">
        <option value="0">Sélectionner</option>
        {% for school in schools_list %}
        <option value="{{ school.id }}" 
        {% if selected_school == school.id %} selected {% endif %}>{{ school }}</option>
        {% empty %}
        {% endfor %}
      </select>
    </div>
  </div>

  <div class="form-group row">
    <div class="col-8">
      <label for="search" class="pl-2">Rechercher:</label>
      <input type="search" name="search" id="search" 
              class="form-control btn-sm ml-2"
              value="{{ search }}"
              hx-get="{{ request.path }}" 
              hx-target="#main-content"
              hx-include="[name=per_page], [name=locations_select], [name=schools_select]">
    </div>
    <div class="col-4">
      <label for="per_page" class="pl-2">Afficher</label>
      <select name="per_page" id="per_page" class="form-control form-control-sm" hx-get="{{ request.path }}?page={{ page }}"
              hx-target="#main-content" hx-include="[name=search], [name=locations_select], [name=schools_select]">
        <option value="10" {% if per_page|floatformat:'0' == '10' %} selected="selected" {% endif %}>10 éléments</option>
        <option value="25" {% if per_page|floatformat:'0' == '25' %} selected="selected" {% endif %}>25 éléments</option>
        <option value="50" {% if per_page|floatformat:'0' == '50' %} selected="selected" {% endif %}>50 éléments</option>
        <option value="100" {% if per_page|floatformat:'0' == '100' %} selected="selected" {% endif %}>100 éléments</option>
      </select>
    </div>
  </div>

  {% if result_found %}
  <div class="alert alert-warning font-weight-bold d-flex justify-content-between border">
    <span><span data-feather="filter" class="feather-16 align-middle mr-2"></span> {{ result_found }} résultat(s)</span>
  </div>
  {% endif %}
<div class="row" hx-get="{{ request.path }}" hx-trigger="saved from:body" hx-target="#main-content">
  <div class="col">
    <div class="card mb-grid">
      <div class="table-responsive-md">
        <table class="table table-sm d-table-sm table-actions table-striped table-bordered table-hover mb-0" data-table hx-boost="true">
          <thead class="bg-secondary">
            <tr>
              {% if user.role != ROLE_ECOLE %}
                <th scope="col" class="text-white">COMMISSION</th>
              {% endif %}
              <th scope="col" class="text-white">DRENA</th>
              <th scope="col" class="text-white">École</th>
              <th scope="col" class="text-white text-center">Classes</th>
              <th scope="col" class="text-white text-center">Total élèves</th>
              <th scope="col" class="text-white text-center">Garçons</th>
              <th scope="col" class="text-white text-center">Filles</th>
              <th scope="col" class="text-white text-center">Date mise à jour</th>
              <th scope="col" class="text-white">Actions</th>
            </tr>
          </thead>
          <tbody id="tbody">
              {% for stat in statistics %}
              <tr>
                {% if user.role != ROLE_ECOLE %}
                  <td class="align-middle">{{ stat.school.local_commission }}</td>
                {% endif %}
                <td class="align-middle">{{ stat.school.drena_obj }}</td>
                <td class="align-middle">{{ stat.school.name }}</td>
                <td class="align-middle text-center">{{ stat.levels_count|default:"-" }}</td>
                <td class="align-middle text-center"><strong>{{ stat.students_count|default:"-" }}</strong></td>
                <td class="align-middle text-center">{{ stat.boys_count|default:"-" }}</td>
                <td class="align-middle text-center">{{ stat.girls_count|default:"-" }}</td>
                <td class="align-middle text-center">{{ stat.date_updated|date:"d/m/Y H:i" }}</td>
                <td class="align-middle text-center">
                  <div class="dropdown">
                    {% if perms.exams.change_schoolstatistics %}
                      <button class="btn btn-warning btn-sm" 
                              hx-get="{% url 'statistics_edit' stat.id %}" 
                              hx-target="#dialog"><i data-feather="edit"></i></button>
                    {% endif %}
                  </div>
                </td>
              </tr>
              {% endfor %}
          </tbody>
        </table>
        {% include 'components/pagination.html' with include_items="[name=per_page], [name=search], [name=locations_select], [name=schools_select]" %}

      </div>
    </div>
  </div>
</div>
</div>

<script>
if (typeof(feather) !== 'undefined') {
feather.replace();
}
</script>
