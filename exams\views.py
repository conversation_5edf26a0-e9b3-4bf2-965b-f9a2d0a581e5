import os
from typing import Any, Dict
from django.contrib.auth import mixins, decorators, get_user_model, hashers
from django.contrib.auth.models import Group
from django.core.cache import cache
from django.db import models, transaction
from django.db.models import Sum, Count, Q, F, <PERSON><PERSON><PERSON><PERSON>, Value, Exists, OuterRef, Subquery, Case, When
from django.db.models.functions import Concat, Coalesce
from django.db.models.functions import Concat
from django.forms import BaseModelForm
from django.http import HttpRequest, HttpResponseRedirect, HttpResponse, FileResponse
from django.core.files.storage import FileSystemStorage
from django_htmx.http import HttpResponseClientRefresh
from django.shortcuts import render, get_object_or_404
from django.urls import reverse, reverse_lazy
from django.views import generic
from sweetify import sweetify
from openpyxl import Workbook, load_workbook
from . import models, forms
from project_utils import custom_utils, constants, reports


class BaseHTMXView(generic.View):
    def get_template_names(self):
        if bool(self.request.htmx):
            return self.template_name
        return ['full_template.html']

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(*args, **kwargs)
        context['template_name'] = self.template_name
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        return context


class CustomPaginatedListView(BaseHTMXView, generic.ListView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.search_fields = []

    def get_paginate_by(self, queryset):
        page_size = self.request.GET.get('per_page')
        if str(page_size).isnumeric():
            return int(page_size)
        return 10

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        search_value = self.request.GET.get('search')
        page_size = self.request.GET.get('per_page')

        if search_value:
            context['search'] = search_value
        if page_size:
            context['per_page'] = page_size

        return context

    def get_queryset(self):
        return self.filter_by_search_value()

    def filter_by_search_value(self):
        search_value = self.request.GET.get('search')
        new_qs = self.model.objects.none()
        if self.search_fields and search_value:
            for field in self.search_fields:
                if not new_qs.exists():
                    filter_expr = {str(field) : search_value}
                    if not str(field).endswith('iexact'):
                        filter_expr = {f'{field}__icontains': search_value}
                    new_qs = self.model.objects.filter(**filter_expr)
            return new_qs
        return super().get_queryset()


class HomePageView(mixins.LoginRequiredMixin, generic.TemplateView):
    def get_template_names(self):
        if bool(self.request.htmx):
            return ['components/main.html']
        return ['index.html']


    def get_staff_stats(self, year, context):
        user = self.request.user
        qs = models.Staff.objects.filter(year=year) \
            .aggregate(
                hr_total=Count('id', filter=Q(location__in=user.locations.all()) if user.role == constants.ROLE_DELEGATE else Q()),
                hr_male=Count('id', filter=Q(gender=constants.GENDER_MALE) & Q(location__in=user.locations.all()) if user.role == constants.ROLE_DELEGATE else Q()),
                hr_female=Count('id', filter=Q(gender=constants.GENDER_FEMALE) & Q(location__in=user.locations.all()) if user.role == constants.ROLE_DELEGATE else Q()),
                hr_pending=Count('id', filter=Q(status=models.Staff.STATUS_PENDING) & Q(location__in=user.locations.all()) if user.role == constants.ROLE_DELEGATE else Q()),
                hr_manufactured=Count('id', filter=Q(status=models.Staff.STATUS_MANUFACTURED) & Q(location__in=user.locations.all()) if user.role == constants.ROLE_DELEGATE else Q()),
                hr_shipped=Count('id', filter=Q(status=models.Staff.STATUS_SHIPPED) & Q(location__in=user.locations.all()) if user.role == constants.ROLE_DELEGATE else Q()),
            )
        context['stats'] = qs
        return context

    def get_cards_stats(self, year, context):
        qs = models.StudentCard.objects.filter(year=year)
        if self.request.user.role == constants.ROLE_COMMISSION_LOCALE:
            qs = qs.filter(school__local_commission=self.request.user.localcommission)

        qs = qs.aggregate(
                cepe_cards=Count('id', filter=Q(enrollment__exam=constants.EXAM_CEPE) & Q(enrollment__confirmed=True)),
                cepe_shipped=Count('id', filter=Q(status=models.StudentCard.STATUS_SHIPPED) & Q(enrollment__exam=constants.EXAM_CEPE) & Q(enrollment__confirmed=True)),
                cepe_delivered=Count('id', filter=Q(status=models.StudentCard.STATUS_MANUFACTURED) & Q(enrollment__exam=constants.EXAM_CEPE) & Q(enrollment__confirmed=True)),
                bepc_cards=Count('id', filter=Q(enrollment__exam=constants.EXAM_BEPC)),
                bepc_shipped=Count('id', filter=Q(status=models.StudentCard.STATUS_SHIPPED) & Q(enrollment__exam=constants.EXAM_BEPC) & Q(enrollment__confirmed=True)),
                bepc_delivered=Count('id', filter=Q(status=models.StudentCard.STATUS_MANUFACTURED) & Q(enrollment__exam=constants.EXAM_BEPC) & Q(enrollment__confirmed=True)),
                other_cards=Count('id', filter=Q(enrollment__isnull=True)),
                other_shipped=Count('id', filter=Q(status=models.StudentCard.STATUS_SHIPPED) & Q(enrollment__isnull=True)),
                other_delivered=Count('id', filter=Q(status=models.StudentCard.STATUS_MANUFACTURED) & Q(enrollment__isnull=True)),
            )

        qs['cepe_manufactured'] = qs['cepe_delivered'] + qs['cepe_shipped']
        qs['bepc_manufactured'] = qs['bepc_delivered'] + qs['bepc_shipped']
        qs['other_manufactured'] = qs['other_delivered'] + qs['other_shipped']
        context['cards'] = qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = custom_utils.get_selected_year(self.request)
        user = self.request.user
        if user.role != constants.ROLE_DELEGATE:
            context = custom_utils.get_statistics_into_context(year, user=user)
            self.get_cards_stats(year, context)
        else:
            context = self.get_staff_stats(year, context)
        context['active_year'] = year.short_name
        context['section'] = 'home'
        context['CEPE_TRANSLATION'] = constants.CEPE_TRANSLATION
        context['BEPC_TRANSLATION'] = constants.BEPC_TRANSLATION
        context['BAC_TRANSLATION'] = constants.BAC_TRANSLATION
        context['CENTER_TRANSLATION'] = constants.CENTER_TRANSLATION
        context['ROLE_COMMISSION_LOCALE'] = constants.ROLE_COMMISSION_LOCALE
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        context['ROLE_ECOLE'] = constants.ROLE_ECOLE
        if user.role == constants.ROLE_COMMISSION_LOCALE:
            if models.Enrollment.candidates.unconfirmed(
                year, user, confirmed=False
            ).exists():
                context['has_invalid_candidates'] = True
        context['centers_count'] = models.Center.objects \
            .get_for_year(user=user).count()

        role = self.request.user.role
        if role != constants.ROLE_ECOLE and self.request.user.has_perm('exams.view_results'):
            cepe_statistics = models.Center.objects.get_for_year(
                year, self.request.user, annotate=False, annotate_stats=True,
                exam=constants.EXAM_CEPE
            ).aggregate(
                candidates=Sum('candidates'),
                present=Sum('present'),
                admitted=Sum('all_admitted'),
                failed=Sum('failed'),
                missing=Sum('missing'),
                perc=(Sum('all_admitted') / Sum('present')) * 100.0,
            )
            bepc_statistics = models.Center.objects.get_for_year(
                year, self.request.user, annotate=False, annotate_stats=True,
                exam=constants.EXAM_BEPC
            ).aggregate(
                candidates=Sum('candidates'),
                present=Sum('present'),
                admitted=Sum('all_admitted'),
                failed=Sum('failed'),
                missing=Sum('missing'),
                perc=(Sum('all_admitted') / Sum('present')) * 100.0,
            )
            bac_statistics = models.Center.objects.get_for_year(
                year, self.request.user, annotate=False, annotate_stats=True,
                exam=constants.EXAM_BAC
            ).aggregate(
                candidates=Sum('candidates'),
                present=Sum('present'),
                admitted=Sum('all_admitted'),
                failed=Sum('failed'),
                missing=Sum('missing'),
                perc=(Sum('all_admitted') / Sum('present')) * 100.0,
            )
            context['cepe_statistics'] = cepe_statistics
            context['bepc_statistics'] = bepc_statistics
            context['bac_statistics'] = bac_statistics

        if role == constants.ROLE_ECOLE:
            context['custom_message'] = year.school_message or ''
            school = user.school
            infos_needed = not school.drena_obj or not school.iepp
            if infos_needed:
                context['infos_needed'] = True
                context['form'] = forms.SchoolDRENAForm()
        elif role == constants.ROLE_COMMISSION_LOCALE:
            context['custom_message'] = year.commissions_message or ''

        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('espace_choice'))


class CandidatesListView(mixins.LoginRequiredMixin, generic.ListView):
    context_object_name = 'enrollments'

    def get_paginate_by(self, queryset):
        page_param = self.request.GET.get('per_page')
        if page_param:
            return int(page_param)
        return 10

    def get_queryset(self):
        user = self.request.user
        year = custom_utils.get_selected_year(self.request)

        exam = self.kwargs.get('exam')
        qs = models.Enrollment.candidates.get_candidates(
            user=user, year=year, exam=exam, confirmed=False
        )
        selected_school = self.request.GET.get('schools_select')
        selected_location = self.request.GET.get('locations_select')
        if selected_school == '0' or selected_school == 0:
            selected_school = ''
        if selected_location == '0' or selected_location == 0:
            selected_location = ''

        if selected_school and selected_school:
            qs = qs.filter(school__id=selected_school)
        elif user.role == constants.ROLE_COMMISSION_NATIONALE and selected_location and selected_location:
            qs = qs.filter(school__local_commission__id=selected_location)

        search = self.request.GET.get('search')
        qs = qs.annotate(
            full_name=Concat('student__last_name', Value(' '), 'student__first_name', output_field=CharField())
        )

        if search:
            qs = qs.filter(
                Q(full_name__icontains=search) | Q(student__full_name_ar__icontains=search) \
                | Q(student__identifier__icontains=search) | Q(student__matricule__icontains=search)
            )

        return qs.only(
            'confirmed', 'exam_type', 'active', 'student__matricule',
            'student__photo', 'student__identifier', 'student__last_name',
            'student__first_name', 'student__gender', 'student__full_name_ar',
            'student__birth_date', 'student__birth_place', 'student__birth_place_ar',
            'school__name', 'school__local_commission__id'
        ).order_by('student__last_name', 'student__first_name')

    def get_template_names(self):
        if bool(self.request.htmx):
            return ['partials/student/students_list.html']
        return ['full_template.html']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = custom_utils.get_selected_year(self.request)
        context['selected_year'] = year.short_name
        context['active_year'] = year.short_name
        context['exam'] = self.kwargs.get('exam').upper()
        context['section'] = 'candidates'
        context['enrollment_closed'] = not year.can_add_student
        qs = self.get_queryset().only('student__gender', 'confirmed')
        context['boys'] = qs.filter(student__gender=constants.GENDER_MALE).count()
        context['girls'] = qs.filter(student__gender=constants.GENDER_FEMALE).count()
        context['students_valid'] = qs.filter(confirmed=True).count()

        role = self.request.user.role
        selected_school = self.request.GET.get('schools_select')
        selected_location = self.request.GET.get('locations_select')
        if selected_school == '0' or selected_school == 0:
            selected_school = ''
        if selected_location == '0' or selected_location == 0:
            selected_location = ''

        locations = None
        schools = None
        if role != constants.ROLE_ECOLE:
            if role == constants.ROLE_COMMISSION_NATIONALE:
                locations = models.LocalCommission.objects.all()

            if role == constants.ROLE_COMMISSION_NATIONALE and selected_location:
                location = models.LocalCommission.objects.filter(id=int(selected_location)).first()
                schools = models.School.objects.filter(local_commission=location)
            elif role == constants.ROLE_COMMISSION_LOCALE:
                schools = models.School.objects.filter(
                    local_commission=self.request.user.localcommission)

        if schools:
            schools = schools.order_by('name')

        context['locations_list'] = locations
        context['schools_list'] = schools
        if selected_school:
            context['selected_school'] = int(selected_school)
        if selected_location:
            context['selected_location'] = int(selected_location)
        if self.request.GET.get('search'):
            context['search'] = self.request.GET.get('search')
        if self.request.GET.get('per_page'):
            context['per_page'] = self.request.GET.get('per_page')
        if self.request.GET.get('search'):
            context['result_found'] = context.get('object_count')

        context['ROLE_ECOLE'] = constants.ROLE_ECOLE
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        if not bool(self.request.htmx):
            context['template_name'] = 'partials/student/students_list.html'
        return context


class CandidateDetailView(mixins.PermissionRequiredMixin, generic.DetailView):
    permission_required = 'exams.view_student'
    context_object_name = 'enrollment'

    def get_queryset(self):
        user = self.request.user
        year = custom_utils.get_selected_year(self.request)

        return models.Enrollment.candidates.get_candidates(
            user=user, year=year, confirmed=False
        )

    def get_template_names(self):
        if bool(self.request.htmx):
            return ['partials/student/student_detail.html']

    def get(self, request, *args, **kwargs):
        if bool(request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('home'))


@transaction.atomic()
@decorators.permission_required('exams.change_student')
def candidate_edit_view(request, exam :str, enrollment_id=None):
    current_year = custom_utils.get_selected_year(request)
    enrollment = None
    student = None

    exam = exam.lower()

    exam_type = constants.EXAM_TYPE_OFFICIEL
    if enrollment_id:
        enrollment = request.user.school.get_candidates(
        year=current_year, exam=exam.lower(), confirmed=False)\
            .filter(id=enrollment_id).first()
        student = enrollment.student if enrollment else None
        exam_type = enrollment.exam_type

    student_form = forms.StudentForm(
        request.POST or None,
        instance=student, files=request.FILES or None,
        initial={'exam_type': exam_type }
    )

    if request.method == 'POST':
        if student_form.is_valid():
            student = student_form.save(False)
            student.school = request.user.school

            photo = request.FILES.get('photo')
            if photo and int(photo.size / 1024) > 200:
                photo = custom_utils.compress_image(photo,
                    constants.COMPRESSED_IMAGE_HEIGHT,
                    constants.COMPRESSED_IMAGE_HEIGHT)
                student.photo = photo

            certificate = request.FILES.get('certificate')
            if certificate and int(certificate.size / 1024) > 200:
                certificate = custom_utils.compress_image(
                    certificate, constants.CERTIFICATE_IMAGE_WIDTH,
                    constants.CERTIFICATE_IMAGE_HEIGHT
                )
                student.certificate = certificate
            student.save(exam.lower())

            if not enrollment_id:
                student.school = request.user.school
                student.save(exam.lower())

                enrollment = models.Enrollment()
                enrollment.exam = exam.lower()
                enrollment.year = current_year
                enrollment.agent = request.user
                enrollment.school = request.user.school

                enrollment.student = student
                enrollment.save()
            elif enrollment:
                enrollment.exam_type = student_form.cleaned_data['exam_type']
                enrollment.save(update_fields=['exam_type'])

            sweetify.success(request, title='Fait',
                             text='Enregistré avec succès', persistent=True)
            return HttpResponseRedirect(
                reverse('candidates',
                        args=[exam.lower()]))

    context = {
        'form': student_form,
        'exam': exam,
        'active_year': current_year,
    }

    if request.method == 'GET' and bool(request.htmx):
        return render(request, 'partials/student/student_edit.html', context=context)
    return HttpResponseRedirect(
        reverse('candidates', args=[exam.lower()]))


class CandidateStatusChangeView(
    mixins.PermissionRequiredMixin,
    generic.UpdateView):
    fields =  []
    permission_required = 'exams.change_student_status'
    template_name = 'partials/student/student_status_change_confirm.html'

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))

    def get_queryset(self):
        exam = self.kwargs.get('exam').lower()
        year = custom_utils.get_selected_year(self.request)
        return models.Enrollment.candidates.unconfirmed(
            year=year, user=self.request.user, exam=exam, confirmed=False
        )

    def form_valid(self, form):
        year = custom_utils.get_selected_year(self.request)
        enrollment = self.get_object()
        enrollment.confirmed = True
        enrollment.save(update_fields=['confirmed'])
        sweetify.success(self.request, title='Fait', text='Enregistré avec succès',
                         persistent=True)
        exam = self.kwargs.get('exam').lower()
        url = reverse('candidates', args=[exam])
        return HttpResponseRedirect(url)

    def get_context_data(self, **kwargs):
        enrollment = self.get_object()
        context = super().get_context_data(**kwargs)
        if enrollment:
            context['full_name'] = f'{enrollment.student}'
            context['birth_date'] = f'{enrollment.student.birth_date}'
            context['birth_place'] = f"{enrollment.student.birth_place or ''}"
            context['exam'] = f"{self.kwargs.get('exam')}"
        return context

    def get(self, request, *args, **kwargs):
        if bool(request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('home'))


class CandidateStatusCancelView(
    mixins.PermissionRequiredMixin,
    generic.UpdateView):
    fields =  []
    permission_required = 'exams.change_school_status'
    template_name = 'partials/student/student_status_cancel_confirm.html'

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))

    def get_queryset(self):
        year = custom_utils.get_selected_year(self.request)
        return models.Enrollment.candidates.get_candidates(
            year=year, user=self.request.user, confirmed=False
        )

    def form_valid(self, form):
        year = custom_utils.get_selected_year(self.request)
        enrollment = self.get_object()
        enrollment.confirmed = False
        enrollment.save(update_fields=['confirmed'])
        sweetify.success(self.request, title='Fait', text='Enregistré avec succès',
                         persistent=True)
        exam = str(self.get_object().exam).lower()
        url = reverse('candidates', args=[exam])
        return HttpResponseRedirect(url)

    def get_context_data(self, **kwargs):
        enrollment = self.get_object()
        context = super().get_context_data(**kwargs)
        if enrollment:
            context['full_name'] = f'{enrollment.student}'
            context['birth_date'] = f'{enrollment.student.birth_date}'
            context['birth_place'] = f"{enrollment.student.birth_place or ''}"
            context['exam'] = self.get_object().get_exam_display()
        return context

    def get(self, request, *args, **kwargs):
        if bool(request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('home'))


class CandidateCertificateDetailView(CandidateDetailView):
    def get_template_names(self):
        if bool(self.request.htmx):
            return ['partials/student/student_certificate_detail.html']


@decorators.permission_required('exams.change_student_status')
def school_candidates_status_change(request, exam):
    exam = exam.lower()

    if request.method == 'POST':
        form = forms.SchoolListForm(user=request.user, exam=exam, data=request.POST)
        if form.is_valid():
            school_id = form.cleaned_data['school'].id
            exam = form.cleaned_data['exam']
            exam = exam.lower()
            school = models.School.objects.filter(id=school_id).first()
            school.get_candidates(exam=exam, confirmed=False)\
                .filter(confirmed=False).update(confirmed=True)

            text = f"Les Dossiers du {exam.upper()} de l'école {school} " \
                    "ont été validés avec succès"
            sweetify.success(request, title='Fait', text=text, persistent=True)
            year = custom_utils.get_selected_year(request).short_name
            return HttpResponseRedirect(reverse('candidates', args=[exam]))

    form = forms.SchoolListForm(user=request.user, exam=exam)
    if bool(request.htmx):
        return render(
            request,
            'partials/student/student_status_change_confirm_bulk.html',
            context={'form': form}
        )
    return HttpResponseRedirect(reverse('home'))

@decorators.login_required()
def unconfirmed_candidates_list_view(request):
    exam = request.GET.get('exam').lower()
    school = models.School.objects.filter(
        id=request.GET.get('school')).first()
    year = custom_utils.get_selected_year(request)

    qs = models.Enrollment.candidates.unconfirmed(
        year=year, user=request.user, school=school, exam=exam, confirmed=False
    ).order_by('student__last_name', 'student__first_name').all()
    exam_fees = custom_utils.compute_exam_fees(qs.count(), year=year, exam=exam)

    context = {
        'enrollments': qs,
        'unconfirmed_candidates': qs.count(),
        'exam_fees': exam_fees }
    if bool(request.htmx):
        return render(request, 'partials/student/unconfirmed_students_list.html',
            context=context)
    return HttpResponseRedirect(reverse('home'))


class CandidateDeleteView(mixins.PermissionRequiredMixin, generic.DeleteView):
    permission_required = 'exams.delete_student'
    model = models.Enrollment
    template_name = 'partials/student/student_delete_confirm.html'

    def get_queryset(self):
        current_year = custom_utils.get_selected_year(self.request)
        return models.Enrollment.candidates.unconfirmed(
            year=current_year,
            user=self.request.user,
            confirmed=False
        )

    def get_success_url(self):
        obj = self.get_object()
        sweetify.success(self.request, "Fait",
                text="Candidat supprimé avec succès",
                persistent=True)
        return reverse_lazy('candidates',
            args=[str(obj.exam).lower()])

    def get(self, request, *args, **kwargs):
        if bool(self.request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('home'))

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))

    def form_valid(self, form=None):
        obj = self.get_object()
        student = obj.student
        exam = obj.exam
        year = obj.year
        cannot_delete = (self.request.user.role == constants.ROLE_ECOLE) and \
                        (obj.confirmed)

        if cannot_delete:
            sweetify.error(self.request, "Impossible de supprimer l'élève",
                text="Désolé. Ce élève a été approuvé comme candidat." +
                " Vous n'avez donc pas l'autorisation de le supprimer.")
            return HttpResponseRedirect(reverse('candidates', args=[exam]))
        deleted = super().form_valid(form)
        if not models.Enrollment.objects.filter(student=student).exists():
            student.delete()
        return deleted

# School Views
class SchoolsListView(mixins.PermissionRequiredMixin, BaseHTMXView, generic.ListView):
    model = models.School
    context_object_name = 'schools'
    permission_required = 'exams.view_school'
    template_name = 'partials/school/schools_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['schools_count'] = self.get_queryset().count()
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        context['ROLE_COMMISSION_LOCALE'] = constants.ROLE_COMMISSION_LOCALE
        context['ROLE_ECOLE'] = constants.ROLE_ECOLE
        context['section'] = 'schools'
        context['per_page'] = self.request.GET.get('per_page')
        context['info_status'] = self.request.GET.get('info_status')

        # Location filter handling
        selected_location = self.request.GET.get('locations_select')
        if selected_location == '0' or selected_location == 0:
            selected_location = ''

        locations = None
        if self.request.user.role == constants.ROLE_COMMISSION_NATIONALE:
            locations = models.LocalCommission.objects.all()

        context['locations_list'] = locations
        if selected_location:
            context['selected_location'] = int(selected_location)

        search = self.request.GET.get('search')
        if search:
            context['search'] = search
            context['result_found'] = self.get_queryset().count()

        year = custom_utils.get_selected_year(self.request)
        context['active_year'] = year.short_name
        context['enrollment_closed'] = not year.can_add_school
        return context

    def get_queryset(self):
        search = self.request.GET.get('search')
        info_status = self.request.GET.get('info_status')
        selected_location = self.request.GET.get('locations_select')

        qs = models.School.objects.get_for_user(
            self.request.user)

        # Filter by location (only for ROLE_COMMISSION_NATIONALE)
        if (self.request.user.role == constants.ROLE_COMMISSION_NATIONALE and
            selected_location and selected_location != '0'):
            qs = qs.filter(local_commission__id=selected_location)

        if search:
            qs = qs.filter(
                Q(name__icontains=search) | Q(name_ar__icontains=search) \
                | Q(iepp__icontains=search) \
                | Q(name_ar__icontains=search) | Q(director__icontains=search) \
                | Q(identifier=search)
            )

        # Filter by information_updated status
        if info_status == 'complete':
            qs = qs.filter(information_updated=True)
        elif info_status == 'incomplete':
            qs = qs.filter(information_updated=False)

        return qs

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))

    def get_paginate_by(self, queryset):
        page_param = self.request.GET.get('per_page')
        if page_param:
            return int(page_param)
        return 10


class SchoolCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.School
    template_name = 'partials/school/school_edit.html'
    success_url = reverse_lazy('schools')
    permission_required = 'exams.add_school'
    form_class = forms.SchoolEditForm

    def get_form(self, form_class=None):
        kwargs = self.get_form_kwargs()
        kwargs['user'] = self.request.user
        return self.form_class(**kwargs)

    def form_valid(self, form):
        data = form.save(False)
        if self.request.user.role == constants.ROLE_COMMISSION_NATIONALE \
            or custom_utils.get_selected_year(self.request).short_name == '2023':
            data.confirmed = True

        data.save()
        sweetify.success(self.request, title='Enregistré',
                         text='Ecole Enregistrée avec succès', persistent=True)
        return HttpResponseRedirect(reverse('schools'))

    def form_invalid(self, form):
        sweetify.error(self.request, "Formulaire invalide",
                       text="Impossible d'énregistrer les données entrées " \
                       + " Veuillez vous assurer que l'objet *Localité " \
                       + " sélectionné existe dans la liste des localités " \
                       + " et que tous les autres champs sont bien remplis",
                       persistent=True
        )
        return HttpResponseRedirect(reverse('schools'))

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))

    def get(self, request, *args, **kwargs):
        if bool(request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('schools'))

    def get_queryset(self):
        return models.School.objects.get_for_user(self.request.user)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        return context


class SchoolUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    form_class = forms.SchoolEditForm
    model = models.School
    permission_required = 'exams.change_school'
    success_url = reverse_lazy('schools')
    template_name = 'partials/school/school_edit.html'

    def get_form(self, form_class=None):
        kwargs = self.get_form_kwargs()
        kwargs['user'] = self.request.user
        return self.form_class(**kwargs)

    def form_valid(self, form):
        sweetify.success(self.request, title='Enregistré',
            text='Modifications enregistrées avec succès', persistent=True)
        return super().form_valid(form)

    def get(self, request, *args, **kwargs):
        if bool(request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('schools'))

    def get_queryset(self):
        qs = models.School.objects.get_for_user(self.request.user)
        if self.request.user.role == constants.ROLE_COMMISSION_LOCALE \
            and custom_utils.get_selected_year(self.request).short_name != '2023':
            return qs.filter(confirmed=False)
        return qs

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))


class SchoolConfirmView(
    mixins.PermissionRequiredMixin,
    generic.UpdateView):
    model = models.School
    fields =  []
    permission_required = 'exams.change_school_status'
    template_name = 'partials/school/school_status_change_confirm.html'

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))

    def get(self, request, *args, **kwargs):
        if bool(request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('schools'))

    def get_queryset(self):
        return models.School.objects.get_for_user(
            user=self.request.user).filter(confirmed=False)

    def form_valid(self, form):
        enrollment = self.get_object()
        enrollment.confirmed = True
        enrollment.save(update_fields=['confirmed'])
        sweetify.success(
            self.request, title='Fait', text='Espace école crée avec succès',
            persistent=True)
        return HttpResponseRedirect(reverse('schools'))


# Local Commission views
class LocalCommissionListView(mixins.PermissionRequiredMixin,
                              BaseHTMXView, generic.ListView):
    model = models.LocalCommission
    context_object_name = 'commissions'
    permission_required = 'exams.change_localcommission'
    template_name = 'partials/commissions/commissions_list.html'

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['schools_count'] = self.get_queryset().count()
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        context['section'] = 'commissions'
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        return context


@transaction.atomic()
@decorators.permission_required('exams.add_localcommission')
def local_commission_edit_view(request, commission_id=None):
    instance = None
    initial_data = {}

    if commission_id:
        instance = get_object_or_404(models.LocalCommission, id=commission_id)

    if instance:
        initial_data['director'] = instance.get_full_name()
        initial_data['phone'] = instance.phone
        initial_data['location'] = instance.location

    form = forms.LocalCommissionEditForm(
        request.POST or None, initial=initial_data, instance=instance)

    if request.method == 'GET' and bool(request.htmx):
        return render(request, 'partials/commissions/commission_edit.html',
            context={'form': form})
    elif request.method == 'POST':
        if form.is_valid():
            cd = form.cleaned_data
            parts = str(cd['director']).strip().upper().split(' ')
            last_name = parts[0]
            first_name = ' '.join(parts[1:]) if len(parts) > 1 else ' '.join(parts)
            password = str(cd['phone']).strip()

            if not commission_id:
                user = get_user_model().objects.create(
                    username=str(cd['location']).strip().upper(),
                    password=hashers.make_password(str(cd['phone']).strip()),
                    last_name=last_name, first_name=first_name,
                    role=constants.ROLE_COMMISSION_LOCALE
                )
                group, created = Group.objects\
                    .get_or_create(name__iexact='Commission Locale')
                group.user_set.add(user)
                models.LocalCommission.objects.create(
                    user=user, initial_password=password,
                    location=cd['location'], phone=cd['phone']
                )
            else:
                instance.user.first_name = first_name
                instance.user.last_name = last_name
                phone = str(cd['phone']).strip()
                password = hashers.make_password(phone)
                instance.user.password = password
                instance.phone = phone
                instance.location = cd['location']
                instance.user.save(
                    update_fields=['first_name', 'last_name', 'password'])

                instance.initial_password = phone
                instance.save(update_fields=['phone', 'location', 'initial_password'])

            sweetify.success(request, title='Fait',
                text='Enregistré avec succès',
                persistent=True)
    return HttpResponseRedirect(reverse('commissions'))


class YearPricingUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    fields = ['price_cepe', 'price_bepc', 'price_bac', 'school_fees']
    model = models.Year
    permission_required = 'exams.change_year'
    template_name = 'partials/year/pricing_form.html'
    success_url = reverse_lazy('home')

    def form_valid(self, form):
        sweetify.toast(self.request, 'Enregistré',
            text='Modifications enregistrées avec succès!')
        return super().form_valid(form)

    def get(self, request, *args, **kwargs):
        current_year = str(custom_utils.get_selected_year(self.request).short_name)
        if bool(self.request.htmx) and str(self.kwargs['pk'] == current_year):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('home'))

    def get_queryset(self):
        return models.Year.objects.filter(is_current=True)

    def get_object(self, queryset=None):
        return custom_utils.get_selected_year(self.request)

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))


class ScrollingMessageUpdateView(YearPricingUpdateView):
    template_name = 'partials/year/comment_edit_form.html'
    fields = ['school_message', 'commissions_message']


class PaymentBaseView(generic.View):
    def get_queryset(self):
        year = custom_utils.get_selected_year(self.request)
        return models.SchoolPayment.objects.get_queryset(
            self.request.user, year=year)

    def get_success_url(self):
        return reverse_lazy('payments')

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))

    def form_valid(self, form):
        payment = form.save(False)
        year = custom_utils.get_selected_year(self.request)
        if not payment.id:
            payment.year = year
        payment.save()
        sweetify.success(self.request, title='Fait',
            text='Versement enregistré avec succès!',
            persistent=True)
        return HttpResponseRedirect(self.get_success_url())


class PaymentsListView(
    mixins.PermissionRequiredMixin,
    PaymentBaseView,
    BaseHTMXView, generic.ListView):
    model = models.SchoolPayment
    permission_required = 'exams.view_schoolpayment'
    template_name = 'partials/payment/schools_payments_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        current_year = custom_utils.get_selected_year(self.request)
        context['year'] = current_year
        context['active_year'] = current_year.short_name
        context['section'] = 'payments'
        context['ROLE_ECOLE'] = constants.ROLE_ECOLE
        context['ROLE_COMMISSION_LOCALE'] = constants.ROLE_COMMISSION_LOCALE
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        context['payments_total'] = models.SchoolPayment.objects.get_total(
        user=self.request.user, year=current_year) or 0
        return context


class PaymentCreateView(
    mixins.PermissionRequiredMixin,
    PaymentBaseView,
    generic.CreateView):

    permission_required = 'exams.add_schoolpayment'
    model = models.SchoolPayment
    fields = ['date', 'school', 'amount']
    template_name = 'partials/payment/payment_edit.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['location_form'] = forms.LocationsForm()
        return context

    def form_valid(self, form):
        saved = super().form_valid(form)
        cache.delete('payments')
        return saved

class PaymentUpdateView(
    mixins.PermissionRequiredMixin,
    PaymentBaseView,
    generic.UpdateView):

    permission_required = 'exams.change_schoolpayment'
    model = models.SchoolPayment
    fields = ['date', 'school', 'amount']
    template_name = 'partials/payment/payment_edit.html'

    def get_queryset(self):
        current_year = custom_utils.get_selected_year(self.request)
        return models.SchoolPayment.objects.get_queryset(
            self.request.user, year=current_year)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        locations = models.LocalCommission.objects.all()
        context['locations'] = locations
        context['updating'] = True
        context['location_id'] = self.get_object().school.local_commission.id
        return context

    def form_valid(self, form):
        saved = super().form_valid(form)
        cache.delete('payments')
        return saved


class CandidateSpaceView(generic.TemplateView):
    template_name = 'partials/student/student_space.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        return context

    def get(self, request, *args, **kwargs):
        if not bool(self.request.htmx):
            return HttpResponseRedirect(reverse('espace_choice'))
        return super().get(request, *args, **kwargs)


def candidate_infos_view(request):
    form = forms.StudentIdentifierInputForm(request.POST or None)
    if request.method == 'POST':
        return HttpResponseRedirect(reverse('login'))

    context = {
        'form': form, 'description': 'Vérifier mes infos',
        'button_text': 'Voir',
        'infos': True}
    return render(request, 'partials/student/student_space_form.html', context)

def candidate_result_view(request):
    form = forms.StudentIdentifierInputForm(request.POST or None)
    if request.method == 'POST' and form.is_valid():
        identifier = form.cleaned_data['identifier']
        queryset = models.Enrollment.objects.filter(
        confirmed=True, active=True, year=custom_utils.get_selected_year(request),
        center__isnull=False, table_num__isnull=False
        ).filter(Q(table_num=identifier) | Q(student__identifier=identifier))

        enrollment = None
        if queryset.exists():
            enrollment = queryset.first()

        return render(request, 'partials/student/student_result_check.html',
                        {'enrollment': enrollment})
    context = {
        'form': form, 'description': 'Consulter mon résultat',
        'button_text': 'Vérifier',
        'result': True}
    return render(request, 'partials/student/student_space_form.html', context)

def candidate_infos_check_view(request):
    identifier = request.GET.get('identifier')

    if identifier:
        identifier = str(identifier).strip().upper()

    year = custom_utils.get_selected_year(request)
    queryset = models.Enrollment.objects.filter(
        year=year).filter(
            Q(student__identifier=identifier) | \
            Q(table_num=identifier)) \
        .select_related('student')

    context = {}
    if queryset.exists():
        enrollment = queryset.first()
        context['enrollment'] = enrollment
        context['found'] = True
        context['active_year'] = custom_utils.get_selected_year(request).short_name
        context['exam'] = enrollment.get_exam_display()
        context['exam_type'] = enrollment.get_exam_type_display()
    return render(request, 'partials/student/student_infos_check.html', context)


def candidate_convocation_view(request):
    form = forms.StudentIdentifierInputForm(request.POST or None)

    if request.method == 'POST' and form.is_valid():
        identifier = form.cleaned_data['identifier']
        queryset = models.Enrollment.objects.filter(
        confirmed=True, active=True, year=custom_utils.get_selected_year(request),
        center__isnull=False, table_num__isnull=False
        ).filter(Q(table_num=identifier) | Q(student__identifier=identifier))

        if not queryset.exists():
            return HttpResponseRedirect(reverse('candidate_space'))

        pdf_path = os.path.join('static', 'pdf')
        if not os.path.exists(pdf_path):
            os.makedirs(pdf_path)

        pdf_file = os.path.join(pdf_path, 'convocation.pdf')
        pdf = reports.Convocation(orientation='P', unit='mm', queryset=queryset)
        pdf.output(pdf_file)

        return FileResponse(
            open(pdf_file, 'rb'), as_attachment=True,
                content_type='application/pdf')
    context = {
        'form': form, 'description': 'Ma convocation',
        'button_text': 'Imprimer',
        'infos': False}
    return render(request, 'partials/student/student_space_form.html', context)


@decorators.login_required()
def candidates_list_pdf(request, exam :str):
    exam = exam.lower()

    year = custom_utils.get_selected_year(request)

    school = None
    school_id = request.GET.get('school_id')
    if school_id:
        school = get_object_or_404(models.School, pk=school_id)

    confirmed = 'valides' in request.GET
    candidates = models.Enrollment.candidates.get_candidates(
        year = year, user = request.user, exam=exam,
        confirmed=confirmed, school=school
    ).order_by('-confirmed', 'student__last_name', 'student__first_name')

    pdf_path = os.path.join('static', 'pdf')
    if not os.path.exists(pdf_path):
        os.makedirs(pdf_path)

    location = None
    filename = f'candidats_cherifla_{exam}'

    role = request.user.role
    if role == constants.ROLE_ECOLE:
        if not school:
            school = request.user.school
        filename = f'candidats_ecole_{request.user.school.id}_{exam}'
    elif role == constants.ROLE_COMMISSION_LOCALE:
        filename = f'candidats_localite_{request.user.localcommission.id}_{exam}'
        location = request.user.localcommission

    pdf_file = os.path.join(pdf_path, f'{filename}.pdf')
    pdf = reports.CandidatesListPDF(orientation='L')
    pdf.add_content(candidates, year=year,
        location=f'{location}', school=school, exam=exam)
    pdf.output(pdf_file)

    return FileResponse(
        open(pdf_file, 'rb'), as_attachment=True,
            content_type='application/pdf')


@decorators.permission_required('exams.view_school')
def schools_list_pdf(request):
    user = request.user
    schools = models.School.objects.get_for_user_with_students_count(user) \
        .order_by('local_commission__location', 'drena', 'name', 'iepp')

    filename = f'ecoles_cherifla_{user.id}'
    pdf_path = os.path.join('static', 'pdf')
    if not os.path.exists(pdf_path):
        os.makedirs(pdf_path)
    location = None

    if user.role == constants.ROLE_COMMISSION_LOCALE:
        filename = f'ecoles_localite_{request.user.localcommission.id}'
        location = request.user.localcommission

    pdf_file = os.path.join(pdf_path, f'{filename}.pdf')
    pdf = reports.SchoolsListPDF(orientation='L')
    year = custom_utils.get_selected_year(request)
    pdf.add_content(location=location, year=year, user=user, queryset=schools)
    pdf.output(pdf_file)

    return FileResponse(
        open(pdf_file, 'rb'), as_attachment=True,
            content_type='application/pdf')


@decorators.login_required()
def payments_list_pdf(request):
    user = request.user
    queryset = models.School.objects \
        .get_for_user_with_students_count(user)

    filename = f'versements_cherifla_{user.id}'
    pdf_path = os.path.join('static', 'pdf')
    if not os.path.exists(pdf_path):
        os.makedirs(pdf_path)
    location = None

    if user.role == constants.ROLE_COMMISSION_LOCALE:
        filename = f'versements_localite_{request.user.localcommission.id}'
        location = request.user.localcommission

    pdf_file = os.path.join(pdf_path, f'{filename}.pdf')
    pdf = reports.PaymentsList(orientation='L')

    # return HttpResponseRedirect(reverse('home'))

    year = custom_utils.get_selected_year(request)
    pdf.add_content(location=location, user=user, year=year, queryset=queryset)
    pdf.output(pdf_file)

    return FileResponse(
        open(pdf_file, 'rb'), as_attachment=True,
            content_type='application/pdf')


class SchoolFeesListView(mixins.PermissionRequiredMixin, generic.ListView):
    model = models.SchoolFees
    template_name = 'partials/school/fees_list.html'
    permission_required = 'exams.view_schoolfees'

    def get_template_names(self):
        if bool(self.request.htmx):
            if self.request.user.role == constants.ROLE_ECOLE:
                return ['partials/school/fees_detail.html']
            return [self.template_name]
        else:
            return ['full_template.html']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        context['full_year'] = custom_utils.get_selected_year(self.request)
        context['paid'] = models.SchoolFees.objects.has_paid_year_fees(
            user=self.request.user
        )
        if self.request.user.role == constants.ROLE_ECOLE and self.request.user.school:
            context['school'] = self.request.user.school.name
        else:
            queryset = models.SchoolFees.objects.get_for_user(
                user=self.request.user
            )
            context['fees'] = queryset
            context['payments_total'] = queryset.aggregate(
                payments_total=Sum('amount')
            )['payments_total'] or 0

        # Set template_name for non-HTMX requests
        if not bool(self.request.htmx):
            if self.request.user.role == constants.ROLE_ECOLE:
                context['template_name'] = 'partials/school/fees_detail.html'
            else:
                context['template_name'] = self.template_name
        return context


class SchoolFeesCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.SchoolFees
    template_name = 'partials/school/school_fees_edit.html'
    permission_required = 'exams.add_schoolfees'
    form_class = forms.SchoolFeesForm

    def get_form(self, form_class=None):
        kwargs = self.get_form_kwargs()
        kwargs['user'] = self.request.user
        return self.form_class(**kwargs)

    def get_initial(self):
        initials = super().get_initial()
        initials['amount'] = custom_utils.get_selected_year(self.request).school_fees or 0
        return initials

    def form_valid(self, form=None):
        form = form.save(commit=False)
        form.agent = self.request.user
        form.year = custom_utils.get_selected_year(self.request)
        form.save()
        cache.delete('school_fees')
        sweetify.success(self.request, title='Fait',
                             text='Enregistré avec succès', persistent=True)
        return HttpResponseRedirect(reverse('school_fees'))

    def form_invalid(self, form=None):
        sweetify.error(self.request, "Formulaire invalide",
                       text="Impossible d'énregistrer les données entrées." \
                       + " Veuillez vous assurer qu'un enregistrement n'existe pas " \
                       + " déjà pour cette école ", persistent=True
        )
        return HttpResponseRedirect(reverse('school_fees'))


class SchoolFeesUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.SchoolFees
    template_name = 'partials/school/school_fees_edit.html'
    permission_required = 'exams.change_schoolfees'
    form_class = forms.SchoolFeesForm

    def get_form(self, form_class=None):
        kwargs = self.get_form_kwargs()
        kwargs['user'] = self.request.user
        return self.form_class(**kwargs)

    def form_valid(self, form=None):
        form.save(commit=True)
        cache.delete('school_fees')
        sweetify.success(self.request, title='Fait',
                             text='Modifié avec succès', persistent=True)
        return HttpResponseRedirect(reverse('school_fees'))


@decorators.login_required()
def fees_list_pdf(request):
    user = request.user
    queryset = models.SchoolFees.objects \
        .get_for_user(user)

    filename = f'cotisations_ecoles_{user.id}'
    pdf_path = os.path.join('static', 'pdf')
    if not os.path.exists(pdf_path):
        os.makedirs(pdf_path)

    location = None
    if user.role == constants.ROLE_COMMISSION_LOCALE:
        filename = f'cotisations_ecoles_localite_{request.user.localcommission.id}'
        location = request.user.localcommission

    pdf_file = os.path.join(pdf_path, f'{filename}.pdf')
    pdf = reports.FeesListPDF(orientation='L')
    year = custom_utils.get_selected_year(request)
    pdf.add_content(location=location, user=user, year=year, queryset=queryset)
    pdf.output(pdf_file)
    return FileResponse(
        open(pdf_file, 'rb'), as_attachment=True,
            content_type='application/pdf')


class CentersListView(mixins.PermissionRequiredMixin, BaseHTMXView, generic.ListView):
    model = models.Center
    context_object_name = 'centers'
    permission_required = 'exams.view_center'
    template_name = 'partials/center/centers_list.html'

    def get_exam(self):
        exam = self.request.GET.get('exam')
        if (exam and exam.lower() in constants.EXAMS_LIST):
            return exam.lower()
        return constants.EXAM_CEPE



    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = custom_utils.get_selected_year(self.request)
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        context['exam'] = custom_utils.clean_exam_selected(self.request)
        context['ROLE_COMMISSION_LOCALE'] = constants.ROLE_COMMISSION_LOCALE
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        context['per_page'] = self.request.GET.get('per_page')
        locations = models.LocalCommission.objects.all()
        if self.request.user.role == constants.ROLE_COMMISSION_LOCALE:
            locations = locations.filter(id=self.request.user.localcommission.id)
        context['locations'] = locations
        selected_location = self.request.GET.get('location')
        if selected_location:
            selected_location = int(selected_location)

        context['selected_location'] = selected_location

        exam = self.request.GET.get('exam')
        if selected_location or self.request.user.role == constants.ROLE_COMMISSION_LOCALE:
            if not selected_location and self.request.user.role == constants.ROLE_COMMISSION_LOCALE:
                selected_location = self.request.user.localcommission.id

            location_schools = models.School.objects.filter(
                local_commission__pk=selected_location
            ).annotate(candidates=Count('enrollment',
                                        filter=Q(enrollment__year=year) & \
                                                Q(enrollment__active=True) & \
                                                Q(enrollment__confirmed=True) & \
                                                Q(enrollment__exam=exam)
                                        ),
                        center_schools_for_year=Count(
                            'school_centers', filter=Q(school_centers__year=year) & \
                                                     Q(school_centers__exam=exam)
                        )
            ).filter(candidates__gt=0, center_schools_for_year=0)
            context['location_schools'] = location_schools

        search = self.request.GET.get('search')
        if search:
            context['search'] = search
            context['result_found'] = self.get_queryset().count()

        user = self.request.user
        if user.role == constants.ROLE_COMMISSION_LOCALE:
            context['candidates_valid'] = models.Enrollment.candidates.get_candidates(
                year=year, user=user, exam=context['exam']
            ).count()
            queryset = models.Center.objects.get_for_year(
                year=year, user=user, exam=context['exam'], annotate=False
            )
            all_complete = not queryset.filter(complete=False).exists()
            context['all_complete'] = all_complete
            context['rooms_candidates'] = queryset.aggregate(
                rooms_count=Sum('room__capacity'))['rooms_count']

            candidates_valid = context['candidates_valid']
            if candidates_valid == None:
                candidates_valid = 0

            rooms_candidates = context['rooms_candidates']
            if rooms_candidates == None:
                rooms_candidates = 0

            context['center_has_problem'] = (candidates_valid > rooms_candidates) or (candidates_valid + rooms_candidates == 0)
        return context

    def get_queryset(self):
        user = self.request.user
        exam = self.get_exam()
        qs = models.Center.objects.get_for_year(user=user, exam=exam) \
            .prefetch_related('room_set') \
            .only(
                'id', 'location__location',
                'school__name', 'school__name_ar',
                'identifier', 'school__local_commission__id',
                'complete', 'comment'
            )

        location = self.request.GET.get('location')
        if location:
            qs = qs.filter(school__local_commission__id=location)

        search = self.request.GET.get('search')
        if search:
            qs = qs.filter(Q(school__name__icontains=search) | Q(school__name_ar__icontains=search))

        return qs

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))

    def get_paginate_by(self, queryset):
        page_param = self.request.GET.get('per_page')
        if page_param:
            return int(page_param)
        return 10


class CenterBaseView(generic.View):
    def form_invalid(self, form):
        sweetify.error(self.request, title='Erreur',
            text="Une erreur s'est produite! Veuillez vous assurer d'avoir " +
            " bien rempli tous les champs et que ce centre n'existe pas déjà.",
            persistent=True)
        return HttpResponseRedirect(reverse('centers'))

    def get_exam(self):
        return custom_utils.clean_exam_selected(self.request)


class CenterCreateView(
    mixins.PermissionRequiredMixin,
    CenterBaseView,
    generic.CreateView):
    model = models.Center
    template_name = 'partials/center/center_edit.html'
    form_class = forms.CenterAddForm
    success_url = reverse_lazy('centers')
    permission_required = 'exams.add_center'

    def form_valid(self, form):
        data = form.save(commit=True)
        data.year = custom_utils.get_selected_year(self.request)
        data.center_schools.set(form.cleaned_data.get('schools'))
        data.save()
        sweetify.success(self.request, title='Fait',
            text='Centre enregistré avec succès!',
            persistent=True)
        cache.delete('centers')
        user = self.request.user
        exam = data.exam
        url = f'{self.success_url}?exam={exam}'
        return HttpResponseRedirect(url)

    def get_context_data(self, **kwargs: Any):
        context = super().get_context_data(**kwargs)
        exam = self.get_exam()
        year = custom_utils.get_selected_year(self.request)
        context['form'].fields['exam'].choices = ((exam.lower(), exam.upper()),)
        this_centers = models.Center.objects.filter(exam=exam,
            year=year)
        qs = models.School.objects.exclude(school_centers__in=this_centers)
        context['form'].fields['schools'].queryset = qs

        user = self.request.user
        qs = models.School.objects.exclude(center__exam=exam, center__year=year)\
            .order_by('name')
        if user.role == constants.ROLE_COMMISSION_LOCALE:
            qs = qs.filter(local_commission=user.localcommission)
            context['form'].fields['location'].queryset = \
                models.LocalCommission.objects.filter(id=user.localcommission.id)
        context['form'].fields['school'].queryset = qs
        return context


@decorators.permission_required('exams.add_localcommission')
def center_check_view(request):
    school = request.GET.get('school')
    center = models.Center.objects.filter(school__id=school)
    if center.exists():
        return HttpResponse('Ce centre existe déjà!')
    return HttpResponse('')


class LocationCentersView(mixins.LoginRequiredMixin, generic.TemplateView):
    def get_template_names(self):
        if self.request.GET.get('uniques'):
            return ['partials/commissions/commission_centers.html']
        return ['partials/commissions/commission_centers_only.html']

    def get_context_data(self, **kwargs: Any):
        context = super().get_context_data(**kwargs)
        location_id = int(self.request.GET.get('location') or 0)
        location = models.LocalCommission.objects.filter(id=location_id).first()
        form = forms.LocationSchoolsForm(location=location, only_schools_having_students=not self.request.GET.get('all_schools'))
        qs = form.fields['school'].queryset
        if self.request.GET.get('uniques'):
            year = custom_utils.get_selected_year(self.request)
            exam = self.request.GET.get('exam')
            form.fields['school'].queryset = qs.exclude(center__exam=exam, center__year=year)
        context['form'] = form
        return context


class LocationSchoolsView(mixins.LoginRequiredMixin, generic.TemplateView):
    template_name = 'partials/commissions/commission_schools.html'

    def get_context_data(self, **kwargs: Any):
        context = super().get_context_data(**kwargs)
        location_id = int(self.request.GET.get('location') or 0)
        commission = models.LocalCommission.objects.filter(id=location_id).first()
        exam = self.request.GET.get('exam')
        form = forms.LocationSchoolsMultiSelect(commission=commission, exam=exam)
        qs = form.fields['schools'].queryset
        form.fields['schools'].queryset = qs.filter(local_commission=commission)
        context['form'] = form
        return context


class CenterUpdateView(
    mixins.PermissionRequiredMixin,
    CenterBaseView,
    generic.UpdateView):
    model = models.Center
    template_name = 'partials/center/center_edit.html'
    form_class = forms.CenterEditForm
    success_url = reverse_lazy('centers')
    permission_required = 'exams.change_center'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        center = self.get_object()
        center_schools = center.center_schools.all()
        if center_schools.exists():
            context['center_schools'] = center_schools
        context['form'].fields['location'].queryset = \
            models.LocalCommission.objects.filter(
            id=center.location.id
        )

        context['form'].fields['school'].queryset = \
            models.School.objects.filter(
                id=center.school.id
            )
        exam = self.get_exam()
        context['form'].fields['exam'].choices = ((exam.lower(), exam.upper()),)
        return context

    def get_form(self, form_class=None):
        kwargs = self.get_form_kwargs()
        kwargs['location'] = self.get_object().location
        kwargs['exam'] = self.get_exam()
        return self.form_class(**kwargs)

    def form_valid(self, form):
        data = form.save(commit=False)
        for item in form.cleaned_data.get('schools'):
            data.center_schools.add(item)
        data.save()
        cache.delete('centers')
        sweetify.success(self.request, title='Fait',
            text='Données du centre modifiées avec succès!',
            persistent=True)
        exam = data.exam
        url = f'{self.success_url}?exam={exam}'
        return HttpResponseRedirect(url)


@decorators.permission_required('exams.add_localcommission')
def year_permissions_list(request):
    year = custom_utils.get_selected_year(request)
    context = {
        'can_add_student': year.can_add_student,
        'can_edit_student': year.can_edit_student,
        'can_add_school': year.can_add_school,
        'can_edit_school': year.can_edit_school,
        'active_year': year.short_name,
        'can_confirm_student': year.can_confirm_student,
        'can_edit_correction': year.can_edit_correction,
        'can_edit_grade': year.can_edit_grade,
        'can_edit_mock_grade': year.can_edit_mock_grade,
        'full_year': year.name,
    }

    if bool(request.htmx):
        template_name = 'partials/year/year_permissions_list.html'
    else:
        template_name = 'full_template.html'
        context['template_name'] = 'partials/year/year_permissions_list.html'
    return render(request, template_name, context)


class YearPermissionsUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    fields = [
        'can_add_student', 'can_edit_student', 'can_confirm_student',
        'can_add_school', 'can_edit_school', 'can_edit_grade', 'can_edit_mock_grade',
        'can_edit_correction'
    ]
    model = models.Year
    permission_required = 'exams.add_localcommission'
    template_name = 'partials/year/permissions_edit_form.html'
    success_url = reverse_lazy('home')

    def form_valid(self, form):
        sweetify.toast(self.request, 'Enregistré',
            text='Modifications enregistrées avec succès!')
        saved = super().form_valid(form)
        return HttpResponseRedirect(reverse('year_permissions'))

    def get(self, request, *args, **kwargs):
        if bool(self.request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('home'))

    def get_queryset(self):
        return models.Year.objects.filter(is_current=True)

    def get_object(self, queryset=None):
        return custom_utils.get_selected_year(self.request)

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))


class RoomsListView(mixins.PermissionRequiredMixin, BaseHTMXView, generic.ListView):
    model = models.Room
    context_object_name = 'rooms'
    permission_required = 'exams.view_room'
    template_name = 'partials/room/rooms_list.html'

    def get_exam(self):
        exam = self.request.GET.get('exam')
        if (exam and exam.lower() in constants.EXAMS_LIST):
            return exam.lower()
        return constants.EXAM_CEPE

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        context['exam'] = self.get_exam()
        return context

    def get_queryset(self):
        user = self.request.user
        exam = self.get_exam()
        return models.Room.objects.get_for_year(user=user, exam=exam)

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))


class RoomBaseView(generic.View):
    def get_success_url(self):
        exam = custom_utils.clean_exam_selected(self.request)
        url = reverse_lazy('rooms')
        return f'{url}?exam={exam}'

    def form_valid(self, form):
        super().form_valid(form)
        exam = form.cleaned_data['center'].exam
        sweetify.success(self.request, title='Fait',
                             text='Enregistré avec succès', persistent=True)
        cache.delete('rooms')
        cache.delete('centers')
        return HttpResponseRedirect(f"{reverse('rooms')}?exam={exam}")

    def form_invalid(self, form):
        center_id = int(form.data.get('center'))
        center = models.Center.objects.filter(id=center_id).first()
        sweetify.error(self.request, title='Erreur',
            text="Impossible d'enregistrer cette salle car elle existe déjà" + \
                " dans ledit centre",
            persistent=True)
        return HttpResponseRedirect(f"{reverse('rooms')}?exam={center.exam}")

    def get(self, request: HttpRequest, *args: str, **kwargs: Any):
        if bool(self.request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('rooms'))

    def get_exam(self):
        return custom_utils.clean_exam_selected(self.request)

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context['exam'] = self.get_exam()
        exam = custom_utils.exam_or_default(context['exam'])
        year = custom_utils.get_selected_year(self.request)
        qs = models.Center.objects.get_for_year(
            year=year, user=self.request.user, exam=exam, annotate=False)
        context['form'].fields['center'].queryset = qs
        context['form'].fields['exam'].choices = (
            (context['exam'].lower(), context['exam'].upper()),
        )
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        return context


class RoomsCreateView(
    mixins.PermissionRequiredMixin,
    RoomBaseView,
    generic.CreateView):
    permission_required = 'exams.add_room'
    template_name = 'partials/room/room_edit.html'
    model = models.Room
    form_class = forms.RoomForm


class RoomsUpdateView(
    mixins.PermissionRequiredMixin,
    RoomBaseView,
    generic.UpdateView):

    permission_required = 'exams.add_room'
    template_name = 'partials/room/room_edit.html'
    model = models.Room
    form_class = forms.RoomForm



class RoomDeleteView(mixins.LoginRequiredMixin, generic.DeleteView):
    model = models.Room
    template_name = 'partials/room/room_delete_confirm.html'

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        return models.Room.objects.get_for_year(
            self.request.user, exam=exam, with_exam=False)\
            .select_related('center')\
            .filter(center__complete=False)

    def get_success_url(self):
        exam = custom_utils.clean_exam_selected(self.request)
        url = reverse_lazy('rooms')
        return f'{url}?exam={exam}'

    def form_valid(self, form):
        super().form_valid(form)
        sweetify.success(self.request, "Fait",
                text="Objet salle supprimé avec succès",
                persistent=True)
        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, form):
        sweetify.error(self.request, title='Erreur',
            text="Impossible de supprimer cette salle.",
            persistent=True)
        return HttpResponseRedirect(reverse('rooms'))

def room_location_centers_view(request):
    exam = custom_utils.clean_exam_selected(request)
    location = request.GET.get('location')
    form = forms.LocationCenterForm(location=location, exam=exam)
    template_name = 'partials/center/location_centers_input.html'

    url = request.GET.get('url')
    if url == 'results':
        template_name = 'partials/result/centers_input.html'
    return render(
        request, template_name,
        {'form': form, 'url': url})


class BatchRoomCreateView(RoomBaseView, generic.FormView):
    template_name = 'partials/room/batch_room_create.html'
    form_class = forms.BatchRoomForm
    permission_required = 'exams.add_room'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['exam'] = self.get_exam()
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE

        user = self.request.user
        user_role = user.role
        if user_role == constants.ROLE_COMMISSION_LOCALE:
            context['form'].fields['location'].queryset = models.LocalCommission.objects.filter(
                pk=user.localcommission.id
            )

        if user_role == constants.ROLE_COMMISSION_NATIONALE and not self.request.GET.get('location'):
            context['form'].fields['center'].queryset = models.Center.objects.none()

        return context

    def form_valid(self, form):
        center = form.cleaned_data['center']
        num_rooms = form.cleaned_data['num_rooms']
        students_per_room = form.cleaned_data['students_per_room']
        starting_number = form.cleaned_data['starting_number']

        # Check if any of the room numbers already exist for this center
        existing_numbers = set(models.Room.objects.filter(
            center=center,
            number__gte=starting_number,
            number__lt=starting_number + num_rooms
        ).values_list('number', flat=True))

        if existing_numbers:
            # Some room numbers already exist
            existing_str = ', '.join(str(num) for num in sorted(existing_numbers))
            sweetify.error(self.request, title='Erreur',
                text=f"Impossible de créer les salles car les numéros suivants existent déjà: {existing_str}",
                persistent=True)
            return HttpResponseRedirect(f"{reverse('batch_room_add')}?exam={center.exam}")

        # Create the rooms
        rooms_to_create = []
        for i in range(num_rooms):
            room_number = starting_number + i
            rooms_to_create.append(models.Room(
                center=center,
                number=room_number,
                capacity=students_per_room
            ))

        # Bulk create the rooms
        models.Room.objects.bulk_create(rooms_to_create)

        # Clear cache and show success message
        cache.delete('rooms')
        cache.delete('centers')
        sweetify.success(self.request, title='Fait',
            text=f'{num_rooms} salles créées avec succès!',
            persistent=True)

        return HttpResponseRedirect(f"{reverse('rooms')}?exam={center.exam}")


class CenterDeleteView(mixins.LoginRequiredMixin, generic.DeleteView):
    model = models.Center
    template_name = 'partials/center/center_delete_confirm.html'

    def get_queryset(self):
        year = custom_utils.get_selected_year(self.request)
        qs = models.Center.objects.filter(year=year, complete=False)
        if self.request.user.role == constants.ROLE_COMMISSION_LOCALE:
            qs = qs.filter(location=self.request.user.localcommission)
        return qs

    def get_success_url(self):
        exam = custom_utils.clean_exam_selected(self.request)
        url = reverse_lazy('centers')
        return f'{url}?exam={exam}'

    def form_valid(self, form):
        super().form_valid(form)
        sweetify.success(self.request, "Fait",
                text="Objet centre supprimé avec succès",
                persistent=True)
        cache.delete('centers')
        cache.delete('rooms')
        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, form):
        sweetify.error(self.request, title='Erreur',
            text="Impossible de supprimer ce centre.",
            persistent=True)
        return HttpResponseRedirect(reverse('rooms'))


@decorators.permission_required('exams.view_center')
def center_candidates_list_pdf(request, center_id, exam):
    user = request.user
    year = custom_utils.get_selected_year(request)
    center_id = int(center_id)
    center = None
    candidates = None
    exam = custom_utils.exam_or_default(exam)
    queryset = models.Center.objects.get_for_year(
        year, user, exam=exam).filter(id=center_id)

    # Check requested document type
    is_anonymat = (request.GET.get('document') == 'anonymat')
    is_fiches_table = (request.GET.get('document') == 'fiches_table')
    is_convocation = (request.GET.get('document') == 'convocations')
    is_fiches_emarg = (request.GET.get('document') == 'fiches_emargement')
    is_fiche_oral = (request.GET.get('document') == 'fiche_oral' )
    is_fiche_ecrit = (request.GET.get('document') == 'fiche_ecrit' )

    room_number = ''
    if str(request.GET.get('salle')).isnumeric():
        room_number = int(request.GET.get('salle'))
    if queryset.exists():
        center :models.Center = queryset.first()
        candidates = center.enrollment_set \
            .select_related('student')\
            .order_by('student__last_name', 'student__first_name')
        if room_number:
            candidates = candidates.filter(room__number=room_number)

    filename = f'candidats_centre_cherifla_{user.id}'
    if is_fiches_table:
        filename = f'fiches_table_{user.id}'
    elif is_convocation:
        filename = f'convocations_candidats_{user.id}'
    elif is_anonymat:
        filename = f'anonymat_candidats_{user.id}'
    elif is_fiches_emarg:
        filename = f'fiches_emargement_{user.id}'
    elif is_fiche_oral:
        filename = f'fiches_report_oral{user.id}'
    elif is_fiche_ecrit:
        filename = f'fiches_report_ecrit{user.id}'

    pdf_path = os.path.join('static', 'pdf')
    if not os.path.exists(pdf_path):
        os.makedirs(pdf_path)

    pdf_file = os.path.join(pdf_path, f'{filename}.pdf')

    if is_fiches_table:
        pdf = reports.FicheTable(orientation='P', queryset=candidates)
        pdf.output(pdf_file)
    elif is_convocation:
        candidates = candidates.order_by('school', 'student__last_name', 'student__first_name')
        pdf = reports.Convocation(queryset=candidates, orientation='P', unit='mm')
        pdf.output(pdf_file)
    elif is_fiches_emarg:
        candidates = candidates.order_by('student__last_name', 'student__first_name')
        pdf = reports.CenterCandidatesSignatureListPDF(
            queryset=candidates, exam=exam, year=year,
            orientation='L', unit='mm', room=room_number)
        pdf.output(pdf_file)
    elif is_fiche_oral:
        candidates = candidates.order_by('student__last_name', 'student__first_name')
        pdf = reports.FicheOralePDF(
            queryset=candidates, exam=exam, year=year, room=room_number,
            orientation='L', unit='mm')
        pdf.output(pdf_file)
    elif is_fiche_ecrit:
        candidates = candidates.order_by('table_num')
        pdf = reports.FicheReportEcrit(
            queryset=candidates, exam=exam, year=year, room=room_number,
            orientation='L', unit='mm')
        pdf.output(pdf_file)
    else:
        pdf = reports.CenterCandidatesListPDF(orientation='L')
        pdf.add_content(
            queryset=candidates, center=center,
            year=year, exam=exam, anonymat=is_anonymat)
        pdf.output(pdf_file)

    return FileResponse(
        open(pdf_file, 'rb'), as_attachment=True,
            content_type='application/pdf')

@decorators.permission_required('exams.view_center')
def center_rooms_candidates_list_pdf(request, center_id, exam):
    user = request.user
    year = custom_utils.get_selected_year(request)
    center_id = int(center_id)
    center = None
    candidates = None
    exam = custom_utils.exam_or_default(exam)
    queryset = models.Center.objects.get_for_year(
        year, user, exam=exam).filter(id=center_id)

    if queryset.exists():
        center :models.Center = queryset.first()

    filename = f'candidats_centre_cherifla_{user.id}'
    pdf_path = os.path.join('static', 'pdf')
    if not os.path.exists(pdf_path):
        os.makedirs(pdf_path)

    if user.role == constants.ROLE_COMMISSION_LOCALE:
        filename = f'candidats_centre_cherifla_{request.user.localcommission.id}'

    pdf_file = os.path.join(pdf_path, f'{filename}.pdf')
    pdf = reports.CenterRoomsCandidatesListPDF(orientation='L')
    pdf.add_content(center=center, year=year, exam=exam)
    pdf.output(pdf_file)

    return FileResponse(
        open(pdf_file, 'rb'), as_attachment=True,
            content_type='application/pdf')


@decorators.login_required()
@transaction.atomic()
def confirm_location_centers(request, location_id, exam):
    exam = custom_utils.exam_or_default(exam)
    year = custom_utils.get_selected_year(request)
    location = models.LocalCommission.objects.filter(id=location_id).first()
    centers = location.center_set.filter(exam=exam)
    for center in centers:
        custom_utils.confirm_center(center)
    sweetify.success(request, title='Fait',
            text='Centres actualisées avec succès!',
            persistent=True)
    return HttpResponseRedirect(reverse('centers'))


class SubjectsListView(mixins.PermissionRequiredMixin, BaseHTMXView, generic.ListView):
    model = models.Subject
    context_object_name = 'subjects'
    permission_required = 'exams.view_subject'
    template_name = 'partials/subject/subjects_list.html'

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        year = custom_utils.get_selected_year(self.request)
        return models.Subject.objects.get_queryset(year=year, exam=exam)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['exam'] = custom_utils.clean_exam_selected(self.request)
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        return context


class SubjectBaseView(mixins.PermissionRequiredMixin, generic.View):
    model = models.Subject
    template_name = 'partials/subject/subject_edit.html'
    fields = ['exam', 'group', 'name', 'translation', 'coefficient', 'order', 'active']
    permission_required = 'exams.change_subject'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        exam = custom_utils.clean_exam_selected(self.request)
        context['form'].fields['exam'].choices = (
            (exam.lower(), exam.upper()),
        )
        context['exam'] = exam
        return context

    def get(self, request, *args: str, **kwargs):
        if not bool(self.request.htmx):
            return HttpResponseRedirect(reverse('subjects'))
        return super().get(request, *args, **kwargs)


class SubjectCreateView(SubjectBaseView, generic.CreateView):
    def form_valid(self, form=None):
        data = form.save(commit=False)
        data.year = custom_utils.get_selected_year(self.request)
        data.save()
        cache.delete('subjects')
        sweetify.success(self.request, title='Fait',
            text='Matière ajoutée avec succès!',
            persistent=True)
        return HttpResponseRedirect(reverse('subjects'))


class SubjectUpdateView(SubjectBaseView, generic.UpdateView):
    def form_valid(self, form=None):
        form.save(commit=True)
        cache.delete('subjects')
        sweetify.success(self.request, title='Fait',
            text='Matière modifiée avec succès!',
            persistent=True)
        return HttpResponseRedirect(reverse('subjects'))


class GradesListView(mixins.PermissionRequiredMixin, CustomPaginatedListView):
    context_object_name = 'centers'
    model = models.Center
    template_name = 'partials/grade/grades_list.html'
    permission_required = 'exams.view_grade'

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        location = self.request.GET.get('locations')
        user = self.request.user
        year = custom_utils.get_selected_year(self.request)
        for_correction = 'for_correction' in self.request.GET

        qs = models.Center.objects \
            .get_for_year(year=year, user=user, exam=exam, annotate=False, for_correction=True) \
            .annotate(
                candidates=Count('enrollment', distinct=True, filter=Q(enrollment__confirmed=True)),
                candidates_marked_mock_exam=Count(
                    'enrollment',
                    filter=Q(enrollment__mock_average__gte=0) & \
                           Q(enrollment__mock_average__isnull=False) & \
                           Q(enrollment__confirmed=True),
                    distinct=True
                ),
                candidates_marked_gen_exam=Count(
                    'enrollment',
                    filter=Q(enrollment__average__gt=0) & \
                           Q(enrollment__confirmed=True),
                    distinct=True
                ),
            )

        if location and location != '0':
            location = models.LocalCommission.objects.filter(id=location).first()
            qs = qs.filter(location=location)

        search = self.request.GET.get('search')
        if search:
            qs = qs.filter(Q(identifier__icontains=search) | Q(school__name__icontains=search))
        return qs.only('identifier', 'school__name', 'location__id', 'school__local_commission__id')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        exam = custom_utils.clean_exam_selected(self.request)
        year = custom_utils.get_selected_year(self.request)
        context['active_year'] = year.short_name
        context['exam'] = exam
        context['locations_list'] = models.LocalCommission.objects.all()
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE

        computed = models.Enrollment.candidates.get_candidates(
            year=year, user=self.request.user, exam=exam
        ).aggregate(
            total=Count('id', filter=Q(center__isnull=False)),
            national_exam_marked=Count('id', filter=Q(average__gt=0) & Q(average__isnull=False), distinct=True),
            mock_exam_marked=Count('id', filter=Q(mock_average__gte=0) & Q(mock_average__isnull=False), distinct=True),
        )
        context['candidates'] = computed['total']
        context['national_exam_marked'] = computed['national_exam_marked']
        context['mock_exam_marked'] = computed['mock_exam_marked']

        selected = self.request.GET.get('locations')
        if selected:
            context['selected_location'] = int(selected)

        return context

    def get_template_names(self):
        if bool(self.request.htmx):
            return [self.template_name]
        return ['full_template.html']


@decorators.permission_required('exams.change_grade')
@transaction.atomic()
def grade_edit_view(request, enrollment_id=None):
    exam = custom_utils.clean_exam_selected(request)
    year = custom_utils.get_selected_year(request)
    form = forms.StudentGradesEditForm(
        request.user, exam, data=request.POST or None)

    context = {'form': form, 'active_year': year.short_name, 'exam': exam}
    if request.method == 'POST':
        # Check form validity
        if form.is_valid():
            data = form.cleaned_data
            center = models.Center.objects.get_for_year(
                    year=year, annotate=False, user=request.user,
                    exam=exam, for_correction=True
                    ).filter(id=data['center'].id).first()
            exists = center.enrollment_set.only('id', 'table_num') \
                .filter(table_num=data['table_num']).exists()

            # Display error message if center doesnt exist
            if not exists:
                context['error'] = 'Aucune correspondance'

            if exists:
                form.save_grades()
                computed = data['center'].enrollment_set.aggregate(
                    count=Count('id', filter=Q(total__gt=0), distinct=True),
                    enrollments=Count('id', distinct=True),
                )
                count = computed['count'] # data['center'].enrollment_set.filter(total__gt=0).count()
                enrollments = computed['enrollments'] # data['center'].enrollment_set.count()
                context['progress'] = f"Progression: {count} / {enrollments}{' (Terminé)' if count == enrollments else ''}"

                no_grades = data['center'].enrollment_set.filter(total=0).order_by('table_num')
                enrollment = None
                if no_grades.exists():
                    enrollment = no_grades.first()
                else:
                    sweetify.success(request, 'Fait',
                        text='Notes du centre enregistrées',
                        persistent=True)
                    return HttpResponseRedirect(f"{reverse('grades')}?exam={exam}")
                form = forms.StudentGradesEditForm(request.user, exam)
                form.initial['table_num'] = enrollment.table_num if enrollment else ''
                form.initial['center'] = data['center'] if data['center'] else None
                context['form'] = form
        else:
            print('Form is not valid')
            print(exam)

    center = None
    center_id = request.GET.get('center')
    table_num = request.GET.get('table_num')
    updating_grade = request.GET.get('updating_grade')
    if updating_grade:
        context['updating_grade'] = True

    if center_id:
        center = models.Center.objects.get_for_year(
            user=request.user, annotate=False, exam=exam, for_correction=True
        ).filter(id=int(center_id))

    if center_id and table_num:
        enrollment = None
        if center.exists():
            center = center.first()
            form.initial['center'] = center
            enrollment = center.enrollment_set \
                .prefetch_related('grade_set') \
                .filter(table_num=table_num).first()

        if enrollment and enrollment.grade_set.exists():
            context['progress'] = 'Modification de notes'
            for grade in enrollment.grade_set.all():
                id = grade.subject.id
                form.initial[f'grade_{id}'] = grade.value
        elif enrollment and not enrollment.grade_set.exists():
            context['error'] = 'Notes non saisies'
        else:
            context['error'] = '* Numéro de table invalide dans ce centre'
            context['updating_grade'] = True

        form.initial[f'table_num'] = table_num
    if bool(request.htmx):
        return render(request, 'partials/grade/grade_edit.html', context)
    context['template_name'] = 'partials/grade/grade_edit.html'
    return render(request, 'full_template.html', context)

@decorators.login_required()
def center_grading_progress_view(request):
    user = request.user
    year = custom_utils.get_selected_year(request)
    center_id = request.GET.get('center') or 0
    exam = models.Center.objects.filter(id=int(center_id)).first().exam
    center = models.Center.objects.get_for_year(
        year=year, annotate=False, user=user, exam=exam, for_correction=True
    ).filter(id=int(center_id)).first()
    count = 0
    enrollments = 0
    if center:
        count = center.enrollment_set.filter(total__gt=0).count()
        enrollments = center.enrollment_set.count()
    return HttpResponse(f"Progression: {count} / {enrollments} {' (Terminé)' if count > 0 and count == enrollments else ''}")

def add_centers_and_locations_to_context(context, request, exam=constants.EXAM_CEPE):
    user = request.user
    if user.role == constants.ROLE_COMMISSION_NATIONALE:
        context['locations'] = models.LocalCommission.objects.all()
    elif user.role == constants.ROLE_COMMISSION_LOCALE:
        qs = models.LocalCommission.objects.filter(
            id=user.localcommission.id)
        context['locations'] = qs

    context['centers'] = models.Center.objects.get_for_year(
        year=custom_utils.get_selected_year(request), user=user,
        annotate=False, exam=exam, for_correction=True
    )
    context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE

@decorators.permission_required('exams.view_grade')
def grade_import_view(request):
    exam = custom_utils.clean_exam_selected(request)
    form = forms.ExcelUploadForm(request.POST or None, files=request.FILES or None)
    done = False
    if request.method == 'POST' and form.is_valid():
        file = request.FILES['excel_file']
        qs = models.Subject.objects.get_queryset(exam)
        subjects = [subject for subject in qs]
        subjects_count = len(subjects)
        center_id = request.POST.get('center')
        if center_id:
            center_id = int(center_id)

        wkb = load_workbook(file)
        wks = wkb.active
        grades_to_create = []
        grades_to_update = []
        enrollments_to_update = []
        for i, row in enumerate(wks.iter_rows(values_only=True)):
            if len(row) - 1 == subjects_count and i > 0:
                table_num = str(row[0]) or 'no_table_num'
                center = models.Center.objects.get_for_year(
                    year=custom_utils.get_selected_year(request), user=request.user,
                    annotate=False, exam=exam, for_correction=True
                ).filter(id=center_id).first()
                enrollment = center.enrollment_set.filter(table_num=table_num)

                total = 0
                average = 0
                if enrollment.exists():
                    enrollment = enrollment.prefetch_related('grade_set').first()
                    grades = list(enrollment.grade_set.all()) or None
                    for index, subject in enumerate(subjects):
                        value = 0
                        if str(row[index + 1]).isnumeric():
                            value = int(row[index + 1])
                            if value > 20:
                                value = 0

                        if grades:
                            grades[index].value = value
                            grades_to_update.append(grades[index])
                            # grades[index].save(update_fields=['value'])
                        else:
                            grade = models.Grade(
                                value=value, enrollment=enrollment,
                                subject=subject)
                            grades_to_create.append(grade)

                        total += value

                    # Bulk create and update

                    # Compute total and update enrollment
                    average = total / subjects_count
                    enrollment.total = total
                    enrollment.average = average
                    custom_utils.update_annual_average(enrollment)
                    enrollments_to_update.append(enrollment)
                    done = True
                    # enrollment.save(update_fields=['total', 'average'])

        models.Grade.objects.bulk_create(grades_to_create)
        models.Grade.objects.bulk_update(grades_to_update, ['value'])
        models.Enrollment.objects.bulk_update(enrollments_to_update, ['total', 'average', 'gen_average'])

    if done:
        sweetify.success(request, 'Fait', text='Importation terminée', persistent=True)
        return HttpResponseRedirect(f"{reverse('grades')}?exam={exam}")
    if bool(request.htmx):
        template_name = 'partials/grade/grade_import.html'
    else:
        template_name = 'full_template.html'
    context = {'active_year': custom_utils.get_selected_year(request).short_name}
    context['import'] = True
    context['form'] = form
    context['action'] = f"{reverse('grade_import')}?exam={exam}"
    context['selected_exam'] = exam
    if not bool(request.htmx):
        context['template_name'] = 'partials/grade/grade_import.html'
    add_centers_and_locations_to_context(context, request, exam)
    return render(request, template_name, context)

@decorators.permission_required('exams.view_grade')
def grade_export_view(request):
    exam = custom_utils.clean_exam_selected(request)
    if bool(request.htmx):
        template_name = 'partials/grade/grade_export.html'
    else:
        template_name = 'full_template.html'

    context = {'active_year': custom_utils.get_selected_year(request).short_name}
    context['import'] = False
    context['action'] = f"{reverse('grades_excel')}?exam={exam}"
    context['selected_exam'] = exam
    if not bool(request.htmx):
        context['template_name'] = 'partials/grade/grade_export.html'
    add_centers_and_locations_to_context(context, request, exam)
    return render(request, template_name, context)


def grades_excel_view(request):
    # Create a new workbook and add a worksheet
    exam = custom_utils.clean_exam_selected(request)
    print(request.GET)
    wb = Workbook()
    ws = wb.active

    # Add column headers
    subjects = models.Subject.objects.get_queryset(exam)
    subjects_count = subjects.count()
    ws['A1'] = 'N_TABLE'

    for index, subject in enumerate(subjects):
        colname = f'{chr(65  + index + 1)}1'
        ws[colname] = f'{str(subject.name).upper().split()[0]}'

    center = request.POST.get('center')
    exam = request.POST.get('exam')

    center = models.Center.objects.get_for_year(
        year=custom_utils.get_selected_year(request),
        exam=exam, user=request.user, annotate=False,
        for_correction=True).filter(id=center)

    if center.exists():
        center = center.first()
    else:
        return HttpResponseRedirect(reverse('grade_export'))

    enrollments = center.enrollment_set.prefetch_related('grade_set').order_by('table_num')

    # Add dummy data
    rows = []

    for enrollment in enrollments:
        row = enrollment.table_num,
        grades = enrollment.grade_set.prefetch_related('subject')
        if enrollment.grade_set.exists():
            for grade in grades:
                row += grade.value,
        else:
            for i in range(subjects_count):
                row += 0,

        rows.append(row)

    for row in rows:
        ws.append(row)

    # Set the filename and content type for the response
    filename = f'centre_{center.id}_notes.xlsx'
    content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

    # Create a response object with the Excel file as its content
    response = HttpResponse(content_type=content_type)
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    # Save the workbook to the response
    wb.save(response)

    return response


class GradesStatisticsByCenterView(generic.ListView):
    context_object_name = 'centers'
    model = models.Center
    template_name = 'partials/result/grades_statistics.html'

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        location = self.request.GET.get('locations')
        user = self.request.user
        year = custom_utils.get_selected_year(self.request)
        qs = models.Center.objects \
            .get_for_year(
                year=year, user=user, exam=exam,
                annotate=False, annotate_stats=True) \
            .order_by('identifier', 'school__name')

        if location:
            location = models.LocalCommission.objects.filter(id=location).first()
            qs = qs.filter(location=location)
        return qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = custom_utils.get_selected_year(self.request)
        context['active_year'] = year.short_name
        computed = self.get_queryset().aggregate(
            total=Sum('candidates'),
            admitted=Sum('all_admitted')
        )
        context['all_candidates'] = computed['total']
        context['all_admitted'] = computed['admitted']
        return context

    def get_template_names(self):
        if bool(self.request.htmx):
            return [self.template_name]
        return ['full_template.html']


class GradesStatisticsByLocationView(generic.ListView):
    context_object_name = 'locations'
    model = models.LocalCommission
    template_name = 'partials/result/grades_statistics_by_location.html'

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        return models.LocalCommission.objects.get_with_results(exam)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = custom_utils.get_selected_year(self.request)
        context['active_year'] = year.short_name
        context['selected_exam'] = custom_utils.clean_exam_selected(self.request)
        context['stats'] = self.get_queryset().aggregate(
            candidates=Sum('candidates'),
            present=Sum('present'),
            admitted=Sum('all_admitted'),
            failed=Sum('failed'),
            missing=Sum('missing'),
            perc=(Sum('all_admitted') / Sum('present')) * 100.0,
        )
        if not bool(self.request.htmx):
            context['template_name'] = self.template_name
        return context

    def get_template_names(self):
        if bool(self.request.htmx):
            return [self.template_name]
        return ['full_template.html']


class ResultsByCenterListView(generic.ListView):
    context_object_name = 'candidates'
    model = models.Enrollment
    template_name = 'partials/result/summarized_results.html'

    def get_user_year(self):
        year = custom_utils.get_selected_year(self.request)
        return year

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        qs = models.Enrollment.objects.none()
        center_id = self.request.GET.get('center')
        if center_id:
            center_id = int(center_id)
        else:
            center_id = 0

        year = self.get_user_year()

        user = self.request.user
        center = models.Center.objects.get_for_year(
            year=year, user=user, exam=exam, annotate=False
        ).filter(id=center_id)

        if center.exists():
            return center.first().enrollment_set \
                .select_related('student') \
                .order_by('-total')
        return qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = self.get_user_year()
        context['active_year'] = year.short_name
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE

        center_id = self.request.GET.get('center')
        location_id = self.request.GET.get('location')
        exam_id = self.request.GET.get('exam')
        context['display_results'] = False
        if center_id:
            context['display_results'] = True
            context['selected_center'] = center_id

        if location_id:
            context['selected_location'] = int(location_id)
        if exam_id:
            context['selected_exam'] = exam_id

        centers = models.Center.objects.get_for_year(
            year=year, user=self.request.user, \
            exam=custom_utils.exam_or_default(exam_id),
            annotate=False
        )
        locations = models.LocalCommission.objects.all()
        context['centers_list'] = centers
        context['locations'] = locations
        context['center'] = centers.filter(id=center_id).first() if centers.filter(id=center_id).exists() else ''

        if self.request.user.role == constants.ROLE_COMMISSION_LOCALE:
            commission_id = self.request.user.localcommission.id
            context['locations'] = locations.filter(id=commission_id)

        context['MIN_AVERAGE'] = year.get_min_avg(
            custom_utils.clean_exam_selected(self.request))
        return context

    def get_template_names(self):
        if bool(self.request.htmx):
            return [self.template_name]
        return ['full_template.html']


class ResultsDocumentsByCenterView(generic.ListView):
    context_object_name = 'centers'
    model = models.Center
    template_name = 'partials/result/centers_results_documents.html'

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        return models.Center.objects.get_for_year(
            year=custom_utils.get_selected_year(self.request),
            user=self.request.user, exam=exam, annotate=False) \
            .prefetch_related('room_set') \
            .order_by('identifier', 'school__name')

    def get_template_names(self):
        if bool(self.request.htmx):
            return [self.template_name]
        return ['full_template.html']

    def get_context_data(self, **kwargs):
        lang = self.request.GET.get('lang') or constants.LANGUAGE_ARABIC.lower()
        context = super().get_context_data(**kwargs)
        context['lang'] = lang
        context['ROLE_COMMISSION_LOCALE'] = constants.ROLE_COMMISSION_LOCALE
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        if not bool(self.request.htmx):
            context['template_name'] = self.template_name
        return context


@decorators.login_required()
def results_list_pdf(request):
    user = request.user
    year = custom_utils.get_selected_year(request)
    for_location = bool(request.GET.get('par_localite'))
    exam = custom_utils.clean_exam_selected(request)
    queryset = models.Center.objects.get_for_year(
        user=user, year=custom_utils.get_selected_year(request),
        annotate_results=True, annotate=False, exam=exam
    ).order_by('location__location', 'identifier', 'school__name')

    summary = None
    if for_location:
        queryset = models.LocalCommission.objects.get_with_detailed_results(exam)
        summary = queryset.aggregate(
                boys_count=Sum('boys_count'),
                girls_count=Sum('girls_count'),
                students_count=Sum('students_count'),
                boys_present=Sum('boys_present'),
                girls_present=Sum('girls_present'),
                students_present=Sum('students_present'),
                boys_admitted=Sum('boys_admitted'),
                girls_admitted=Sum('girls_admitted'),
                students_admitted=Sum('students_admitted'),
                boys_perc=(Sum('boys_admitted') / Sum('boys_present')) * 100.0,
                girls_perc=(Sum('girls_admitted') / Sum('girls_present')) * 100.0,
                students_perc=(Sum('students_admitted') / Sum('students_present')) * 100.0
            )
    filename = f'resultats_{user.id}'
    pdf_path = os.path.join('static', 'pdf')
    if not os.path.exists(pdf_path):
        os.makedirs(pdf_path)

    summarized = request.GET.get('summarized')
    pdf_file = os.path.join(pdf_path, f'{filename}.pdf')

    if summarized:
        pdf = reports.ResultsStatisticsSummarized(orientation='L')
    else:
        pdf = reports.ResultsStatistics(orientation='L')
    pdf.add_content(queryset=queryset, year=year, user=request.user, for_location=for_location, summary=summary)

    pdf.output(pdf_file)
    return FileResponse(
        open(pdf_file, 'rb'), as_attachment=True,
            content_type='application/pdf')

@decorators.login_required()
def candidates_results_list_pdf(request, center_id):
    order = request.GET.get('ordre')
    for_all_centers = request.GET.get('tous')
    summarized = request.GET.get('resume')
    french = (request.GET.get('lang') == 'fr')
    exam = models.Center.objects.filter(id=int(center_id)).first().exam
    user = request.user
    queryset = models.Enrollment.candidates.get_candidates(
        user=user, year=custom_utils.get_selected_year(request),
        exam=exam
    ).filter(center__id=center_id).order_by('-total')

    centers = None
    if for_all_centers and request.user.role == constants.ROLE_COMMISSION_LOCALE:
        queryset = models.Enrollment.candidates \
            .get_candidates(
                user=user, year=custom_utils.get_selected_year(request),
                exam=exam).filter(center__location=request.user.localcommission) \
            .order_by('-total')

    filename = f'resultats_centre{user.id}'
    pdf_path = os.path.join('static', 'pdf')
    if not os.path.exists(pdf_path):
        os.makedirs(pdf_path)

    pdf_file = os.path.join(pdf_path, f'{filename}.pdf')

    if order == 'alpha':
        queryset = queryset.order_by('student__last_name', 'student__first_name')

    pdf = reports.CandidatesResults(
        orientation='L', queryset=queryset,
        centers=centers, user=request.user,
        alpha_order=(order == 'alpha'),
        summarized=summarized, french=french,
        for_location=bool(for_all_centers))

    pdf.output(pdf_file)
    return FileResponse(
        open(pdf_file, 'rb'), as_attachment=True,
            content_type='application/pdf')

# Distinctions
class DistinctionsListView(mixins.PermissionRequiredMixin, generic.ListView):
    model = models.Distinction
    context_object_name = 'distinctions'
    permission_required = 'exams.view_distinction'
    template_name = 'partials/distinction/distinctions_list.html'

    def get_template_names(self):
        if bool(self.request.htmx):
            return [self.template_name]
        return ['full_template.html']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        context['template_name'] = self.template_name
        return context


class DistinctionBaseView(mixins.PermissionRequiredMixin, generic.View):
    model = models.Distinction
    template_name = 'partials/distinction/distinction_edit.html'
    fields = ['average', 'name', 'short_name', 'translation']
    permission_required = 'exams.change_distinction'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        return context

    def get(self, request, *args: str, **kwargs):
        if not bool(self.request.htmx):
            return HttpResponseRedirect(reverse('distinctions'))
        return super().get(request, *args, **kwargs)


class DistinctionCreateView(DistinctionBaseView, generic.CreateView):
    def form_valid(self, form=None):
        form.save(commit=True)
        sweetify.success(self.request, title='Fait',
            text='Mention ajoutée avec succès!',
            persistent=True)
        return HttpResponseRedirect(reverse('distinctions'))


class DistinctionUpdateView(DistinctionBaseView, generic.UpdateView):
    def form_valid(self, form=None):
        form.save(commit=True)
        sweetify.success(self.request, title='Fait',
            text='Mention modifiée avec succès!',
            persistent=True)
        return HttpResponseRedirect(reverse('distinctions'))


class FavouredCandidatesResultsListView(mixins.PermissionRequiredMixin, generic.ListView):
    context_object_name = 'candidates'
    model = models.Enrollment
    template_name = 'partials/result/favoured_list.html'
    permission_required = 'exams.view_results'

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        year = custom_utils.get_selected_year(self.request)
        return models.Enrollment.candidates.favoured(year, self.request.user, exam)

    def get_template_names(self):
        if bool(self.request.htmx):
            return [self.template_name]
        return ['full_template.html']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['ROLE_COMMISSION_LOCALE'] = constants.ROLE_COMMISSION_LOCALE
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        if not bool(self.request.htmx):
            context['template_name'] = self.template_name
        return context


class BulletinByCenterView(mixins.PermissionRequiredMixin, generic.ListView):
    context_object_name = 'centers'
    model = models.Center
    template_name = 'partials/result/bulletins_list.html'
    permission_required = 'exams.view_results'

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        return models.Center.objects.get_for_year(
            year=custom_utils.get_selected_year(self.request),
            user=self.request.user, exam=exam) \
            .order_by('identifier', 'school__name')

    def get_template_names(self):
        if bool(self.request.htmx):
            return [self.template_name]
        return ['full_template.html']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        exam = custom_utils.clean_exam_selected(self.request)
        context['ROLE_COMMISSION_LOCALE'] = constants.ROLE_COMMISSION_LOCALE
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        context['exam'] = exam

        center_id = self.request.GET.get('center')
        if center_id:
            context['display_results'] = True
            context['center_id'] = center_id
        else:
            context['center_id'] = 0

        if center_id:
            context['candidates'] = models.Enrollment.candidates.get_candidates(
                year=custom_utils.get_selected_year(self.request),
                user=self.request.user, exam=exam,
                select_related=False
            ).filter(center__id=center_id) \
            .select_related('student', 'center__location', 'center__school')

        if not bool(self.request.htmx):
            context['template_name'] = self.template_name
        return context


@decorators.permission_required('exams.view_results')
def document_pdf(request, center_id):
    doc_type = request.GET.get('doc_type')
    table_num = request.GET.get('table_num')
    room = request.GET.get('salle')
    user = request.user
    queryset = models.Enrollment.candidates.get_candidates(
        user=user, year=custom_utils.get_selected_year(request)
    ).order_by('-total')

    queryset  = queryset.filter(center__id=center_id)

    if table_num:
        queryset = queryset.filter(table_num=table_num)

    if room:
        queryset = queryset.filter(room__number=int(room))

    filename = f'bulletins_centre{user.id}'
    if table_num:
        filename = f'bulletin'

    if doc_type == 'bulletin':
        part = str(request.GET.get('part'))
        if part and part.isnumeric() and int(part) == 1:
            queryset = queryset[:15]
        elif part and part.isnumeric() and int(part) == 2:
            queryset = queryset[15:]

    elif doc_type == 'conduite':
        filename = f'conduite_centre{user.id}'
        if table_num:
            filename = f'conduite'
    elif doc_type == 'diplome':
        filename = f'diplome_centre{user.id}'
        if table_num:
            filename = f'diplome'


    pdf_path = os.path.join('static', 'pdf')
    if not os.path.exists(pdf_path):
        os.makedirs(pdf_path)

    pdf_file = os.path.join(pdf_path, f'{filename}.pdf')

    if doc_type == 'diplome' or doc_type == 'conduite':
        year :models.Year = queryset.first().year
        min_avg = year.get_min_avg()
        queryset = queryset.filter(Q(gen_average__gte=min_avg) | Q(favoured=True))

    pdf = None
    if doc_type == 'conduite':
        pdf = reports.AttestationBonneConduite(
            orientation='P', queryset=queryset)
    elif doc_type == 'diplome':
        pdf = reports.Diplome(
            orientation='L', queryset=queryset)
    else:
        pdf = reports.Bulletin(
        orientation='P', queryset=queryset)
    pdf.output(pdf_file)
    return FileResponse(
        open(pdf_file, 'rb'), as_attachment=True,
            content_type='application/pdf')


class CorrectionsListView(mixins.PermissionRequiredMixin, generic.ListView):
    context_object_name = 'corrections'
    model = models.StudentCorrection
    template_name = 'partials/correction/corrections_list.html'
    permission_required = 'exams.view_studentcorrection'

    def get_queryset(self):
        user = self.request.user
        year = custom_utils.get_selected_year(self.request)

        exam = self.kwargs.get('exam')
        qs = models.StudentCorrection.objects.get_for_year(
            user, year, exam
        )
        return qs.only(
            'new_last_name_fr', 'new_first_name_fr', 'new_full_name_ar',
            'new_birth_date', 'new_birth_place', 'new_birth_place_ar',
            'new_gender', 'new_photo', 'new_certificate', 'new_exam_type',
            'new_nationality',
        )

    def get_template_names(self):
        if bool(self.request.htmx):
            return [self.template_name]
        return ['full_template.html']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['template_name'] = self.template_name
        context['exam'] = self.kwargs.get('exam')
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        context['corrections_closed'] = not self.request.user.has_perm('add_studentcorrection')
        return context


class CorrectionCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.StudentCorrection
    template_name = 'partials/correction/correction_edit.html'
    form_class = forms.CorrectionForm
    permission_required = 'exams.add_studentcorrection'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        table_num = self.request.GET.get('table_num')

        kwargs['user'] = self.request.user
        if table_num:
            kwargs['table_num'] = table_num
        return kwargs

    @transaction.atomic()
    def form_valid(self, form):
        table_num = form.cleaned_data['table_num']
        queryset = models.Enrollment.candidates.get_candidates(
            year=custom_utils.get_selected_year(self.request),
            user=self.request.user
            ).filter(table_num=table_num)

        if queryset.exists():
            enrollment = queryset.first()
            data = form.save(False)
            data.identifier = enrollment.student.identifier
            data.exam = enrollment.exam
            data.student = enrollment.student
            data.school = enrollment.school
            data.year = enrollment.year

            # Reset dropdown values as they might've been tampered with
            data.initial_nationality = data.student.nationality
            data.initial_gender = data.student.gender
            data.initial_birth_date = data.student.birth_date
            data.initial_exam_type = enrollment.exam_type

            certificate = self.request.FILES.get('new_certificate')
            if certificate and int(certificate.size / 1024) > 200:
                certificate = custom_utils.compress_image(
                    certificate, constants.CERTIFICATE_IMAGE_WIDTH,
                    constants.CERTIFICATE_IMAGE_HEIGHT
                )
                data.initial_certificate = certificate

            photo = self.request.FILES.get('new_photo')
            if photo and int(photo.size / 1024) > 200:
                photo = custom_utils.compress_image(
                    photo, constants.COMPRESSED_IMAGE_WIDTH,
                    constants.COMPRESSED_IMAGE_HEIGHT
                )
                data.new_photo = photo
            data.save()

            sweetify.success(self.request, 'Fait', text='Correction demandée!', persistent=True)
            return HttpResponseRedirect(f"{reverse('corrections', args=[enrollment.exam])}")
        else:
            sweetify.error(self.request, 'Elève inexistant',
                           text="Désolé, mais aucun élève ne possède ce numéro de table. " +
                           "Ou peut-être que vous n'êtes pas autorisé à accéder à la page " +
                           "demandée.", persistent=True)
            return HttpResponseRedirect(reverse('corrections', args=['cepe']))

    def form_invalid(self, form: Any):
        return HttpResponseRedirect(reverse('home'))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['exam'] = self.request.GET.get('exam')
        return context


class CorrectionConfirmView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.StudentCorrection
    fields = ['status']
    template_name = 'partials/correction/correction_confirm.html'
    context_object_name = 'correction'
    permission_required = 'exams.change_correction_status'

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        return models.StudentCorrection.objects.get_for_year(
            self.request.user, custom_utils.get_selected_year(self.request),
            exam=exam
        )

    @transaction.atomic()
    def form_valid(self, form) :
        # Get student
        pk = int(self.kwargs.get('pk'))
        correction = models.StudentCorrection.objects.filter(id=pk).first()
        student = correction.student
        enrollment = models.Enrollment.candidates.get_candidates(
            year=correction.year, user=self.request.user
        ).filter(student=student).first()

        # Save student data
        if correction.new_last_name_fr:
            student.last_name = correction.new_last_name_fr
        if correction.new_first_name_fr:
            student.first_name = correction.new_first_name_fr
        if correction.new_gender:
            student.gender = correction.new_gender
        if correction.new_full_name_ar:
            student.full_name_ar = correction.new_full_name_ar
        if correction.new_birth_date:
            student.birth_date = correction.new_birth_date
        if correction.new_birth_place:
            student.birth_place = correction.new_birth_place
        if correction.new_birth_place_ar:
            student.birth_place_ar = correction.new_birth_place_ar
        if correction.new_nationality:
            student.nationality = correction.new_nationality
        if correction.new_exam_type:
            enrollment.exam_type = correction.new_exam_type

        if correction.new_certificate:
            student.certificate = correction.new_certificate
        if correction.new_photo:
            student.photo = correction.new_photo
        student.save()

        # Update status field value
        correction.status = True
        correction.save(update_fields=['status'])
        enrollment.save(update_fields=['exam_type'])

        sweetify.success(
            self.request, 'Fait',
            text='Demande de correction approuvée!',
            persistent=True)
        return HttpResponseRedirect(reverse('corrections', args=[enrollment.exam]))


class StudentCardListView(mixins.PermissionRequiredMixin, BaseHTMXView, generic.ListView):
    template_name = 'partials/id_card/id_card_list.html'
    permission_required = 'exams.view_student'
    model = models.StudentCard

    def get_paginate_by(self, queryset):
        per_page = self.request.GET.get('per_page')
        if per_page:
            return int(per_page)
        return 10

    def get_context_data(self, **kwargs: Any):
        context = super().get_context_data(**kwargs)
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        context['EXAM_CEPE'] = constants.EXAM_CEPE
        context['EXAM_BEPC'] = constants.EXAM_BEPC
        context['EXAM_BAC'] = constants.EXAM_BAC

        url = reverse('students_cards')
        context['nav_items'] = [
            {'title': 'CLASSES  INTERMEDIAIRES', 'url': f'{url}?niveau=AUTRES', 'code': 'AUTRES'},
            {'title': 'CEPE', 'url': f'{url}?niveau=CM2', 'code': 'CM2'},
            {'title': 'BEPC', 'url': f'{url}?niveau=3EME', 'code': '3EME'},
        ]

        level = self.request.GET.get('niveau')
        context['level'] = level
        queryset = self.get_queryset()

        if self.request.GET.get('search'):
            context['search'] = self.request.GET.get('search')
            context['result_found'] = self.get_queryset().count()

        if self.request.GET.get('per_page'):
            context['per_page'] = self.request.GET.get('per_page')

        year = custom_utils.get_selected_year(self.request)
        context['data'] = context['object_list']
        if queryset.exists():
            aggregated = queryset.aggregate(
                pending=Count('id', filter=Q(year=year) & \
                            Q(status=models.StudentCard.STATUS_PENDING)),
                manufactured=Count('id', filter=Q(year=year) & \
                            Q(status=models.StudentCard.STATUS_MANUFACTURED)),
                shipped=Count('id', filter=Q(year=year) & \
                            Q(status=models.StudentCard.STATUS_SHIPPED)),
                paid=Count('id', filter=Q(year=year) & \
                            Q(card_payment_status=constants.CARD_STATUS_PAID)),
            )
            context['stats'] = aggregated
        return context

    def get_queryset(self):
        year = custom_utils.get_selected_year(self.request)
        queryset = super().get_queryset().filter(year=year) \
            .select_related('school') \
            .annotate(enrollment_exists=Exists(models.Enrollment.objects.filter(studentcard=OuterRef('pk'))))

        user = self.request.user
        if user.role == constants.ROLE_ECOLE:
            queryset = queryset.filter(school__id=user.school_id)
        elif user.role == constants.ROLE_COMMISSION_LOCALE:
            queryset = queryset.filter(school__local_commission=user.localcommission)


        search_value = self.request.GET.get('search')
        if search_value:
            queryset = queryset.annotate(full_name=Concat('last_name', Value(' '), 'first_name', output_field=CharField()))
            queryset = queryset.filter(Q(full_name__icontains=search_value) | Q(matricule_dsps__icontains=search_value) | Q(matricule_cherifla__icontains=search_value))

        level = self.request.GET.get('niveau')
        if level and level != 'AUTRES':
            queryset = queryset.filter(level=level, enrollment_exists=True)
        elif level and level == 'AUTRES':
            queryset = queryset.exclude(enrollment_exists=True)

        if self.request.GET.get('filtre'):
            filters_mapping = {
                'DEMANDES': models.StudentCard.STATUS_PENDING,
                'PRODUITES': models.StudentCard.STATUS_MANUFACTURED,
                'LIVREES': models.StudentCard.STATUS_SHIPPED,
            }
            status_filter = filters_mapping.get(self.request.GET.get('filtre'), models.StudentCard.STATUS_PENDING)
            queryset = queryset.filter(status=status_filter)
        else:
            queryset = queryset.filter(status=models.StudentCard.STATUS_PENDING)

        return queryset.only(
            'id', 'photo', 'matricule_dsps', 'matricule_cherifla',
            'last_name', 'first_name', 'comment', 'gender',
            'status', 'card_payment_status', 'birth_date', 'level',
            'phone', 'date_created', 'school__name',
            'school__drena'
        ).order_by('-date_created')


class StudentCardCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.StudentCard
    fields = [
        'matricule_dsps', 'last_name', 'first_name',
        'gender', 'birth_date', 'birth_place',
        'phone', 'level', 'photo'
    ]
    template_name = 'partials/id_card/id_card_form.html'
    permission_required = 'exams.add_studentcard'

    def get_context_data(self, **kwargs):
        LEVEL_CHOICES = (
            ('CP1', 'CP1'),
            ('CP2', 'CP2'),
            ('CE1', 'CE1'),
            ('CE2', 'CE2'),
            ('CM1', 'CM1'),
            ('CM2', 'CM2'),
            ('6EME', '6EME'),
            ('5EME', '5EME'),
            ('4EME', '4EME'),
            ('3EME', '3EME'),
            ('2NDE', '2NDE'),
            ('1ERE', '1ERE'),
        )
        context = super().get_context_data(**kwargs)
        context['form'].fields['level'].choices = LEVEL_CHOICES
        return context

    def form_valid(self, form):
        print('Form is valid')
        card = form.save(False)
        card.year = custom_utils.get_selected_year(self.request)
        card.school = self.request.user.school
        card.save()
        sweetify.success(self.request, title='Fait',
            text='Enregistré avec succès', persistent=True)
        return HttpResponseRedirect(f"{reverse('students_cards')}?niveau=AUTRES")


class StudentCardUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.StudentCard
    fields = [
        'matricule_dsps', 'last_name', 'first_name',
        'gender', 'birth_date', 'birth_place',
        'phone', 'level', 'photo'
    ]
    template_name = 'partials/id_card/id_card_form.html'
    permission_required = 'exams.change_studentcard'

    def get_context_data(self, **kwargs):
        LEVEL_CHOICES = (
            ('CP1', 'CP1'),
            ('CP2', 'CP2'),
            ('CE1', 'CE1'),
            ('CE2', 'CE2'),
            ('CM1', 'CM1'),
            ('CM2', 'CM2'),
            ('6EME', '6EME'),
            ('5EME', '5EME'),
            ('4EME', '4EME'),
            ('3EME', '3EME'),
            ('2NDE', '2NDE'),
            ('1ERE', '1ERE'),
        )
        context = super().get_context_data(**kwargs)
        context['form'].fields['level'].choices = LEVEL_CHOICES
        return context

    def form_valid(self, form):
        form.save(True)
        sweetify.success(self.request, title='Fait',
            text='Enregistré avec succès', persistent=True)
        return HttpResponseRedirect(f"{reverse('students_cards')}?niveau=AUTRES")


# Create a toggle card paymanet status view
def toggle_card_payment_status(request, pk):
    card = models.StudentCard.objects.filter(id=pk).first()
    if card:
        if card.card_payment_status == constants.CARD_STATUS_PAID:
            card.card_payment_status = constants.CARD_STATUS_UNPAID
        else:
            card.card_payment_status = constants.CARD_STATUS_PAID
        card.save(update_fields=['card_payment_status'])
    return render(request, 'partials/id_card/card_payment_status.html', {'card': card})

class StudentCardDeleteView(mixins.PermissionRequiredMixin, generic.DeleteView):
    model = models.StudentCard
    template_name = 'partials/id_card/delete_confirm.html'
    permission_required = 'exams.delete_studentcard'

    def form_valid(self, form):
        self.get_object().delete()
        sweetify.success(self.request, title='Fait',
            text='Enregistré avec succès', persistent=True)
        return HttpResponseRedirect(f"{reverse('students_cards')}?niveau=AUTRES")


class StudentCardToCandidateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.StudentCard
    template_name = 'partials/id_card/convert_studentcard_to_candidate.html'
    form_class = forms.StudentCardToCandidateForm
    permission_required = 'exams.convert_studentcard'

    def form_valid(self, form):
        with transaction.atomic():
            card = self.get_object()
            student = models.Student(
                matricule=card.matricule_dsps,
                last_name=card.last_name,
                first_name=card.first_name,
                full_name_ar='-',
                birth_date=card.birth_date,
                birth_place=card.birth_place,
                gender=card.gender,
                parent_phone=card.phone,
                school=card.school,
                birth_place_ar='-',
                father='-',
                mother='-',
                photo=card.photo
            )
            student.save(exam=form.cleaned_data.get('exam'))

            models.Enrollment.objects.create(
                student=student,
                year=custom_utils.get_selected_year(self.request),
                exam=form.cleaned_data.get('exam'),
                agent=self.request.user,
                school=card.school
            )

            card.delete()

            sweetify.success(self.request, title='Fait',
                text="Conversion terminée avec succès", persistent=True)
        print(form.cleaned_data)
        return HttpResponseRedirect(f"{reverse('students_cards')}?niveau=AUTRES")


@decorators.login_required()
def send_drena_infos_view(request):
    if request.method == 'POST':
        drena = request.POST.get('drena_obj')
        iepp = request.POST.get('iepp')
        school = request.user.school

        if drena:
            school.drena = str(drena).upper()
        if iepp:
            school.iepp = str(iepp).upper()
        school.save(update_fields=['drena_obj', 'iepp'])
        return HttpResponse(
            """<div class='alert alert-success p-3' id='info-alert'>
            Merci. Les informations de votre école ont été enregistrées avec succès.
            Vous pouvez maintenant faire les demandes de cartes scolaires pour les classes
            intermédiaires dans la rubrique Carte Scolaire.
            </div>
            """)


class TransfertListView(mixins.PermissionRequiredMixin, CustomPaginatedListView):
    model = models.TransferRequest
    context_object_name = 'transferts'
    template_name = 'partials/transfert/transfert_list.html'
    permission_required = 'exams.view_transferrequest'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.search_fields = [
            'enrollment__student__identifier',
            'enrollment__student__matricule',
            'enrollment__student__last_name',
            'enrollment__student__first_name',
        ]

    def get_queryset(self):
        year = custom_utils.get_selected_year(self.request)
        user = self.request.user
        qs = models.TransferRequest.objects.for_user(user=user, year=year)
        qs = self.filter_by_search_value(qs)
        selected_school = self.request.GET.get('schools_select')
        selected_location = self.request.GET.get('locations_select')
        if selected_school == '0' or selected_school == 0:
            selected_school = ''
        if selected_location == '0' or selected_location == 0:
            selected_location = ''

        if selected_school and selected_school:
            qs = qs.filter(enrollment__school__id=selected_school)
        elif user.role == constants.ROLE_COMMISSION_NATIONALE and selected_location and selected_location:
            qs = qs.filter(enrollment__school__local_commission__id=selected_location)
        return qs


    def filter_by_search_value(self, queryset):
        search_value = self.request.GET.get('search')
        new_qs = self.model.objects.none()
        if self.search_fields and search_value:
            for field in self.search_fields:
                if not new_qs.exists():
                    filter_expr = {str(field) : search_value}
                    if not str(field).endswith('iexact'):
                        filter_expr = {f'{field}__icontains': search_value}
                    new_qs = queryset.filter(**filter_expr)
            return new_qs
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        role = self.request.user.role
        selected_school = self.request.GET.get('schools_select')
        selected_location = self.request.GET.get('locations_select')
        if selected_school == '0' or selected_school == 0:
            selected_school = ''
        if selected_location == '0' or selected_location == 0:
            selected_location = ''

        locations = None
        schools = None
        if role != constants.ROLE_ECOLE:
            if role == constants.ROLE_COMMISSION_NATIONALE:
                locations = models.LocalCommission.objects.all()

            if role == constants.ROLE_COMMISSION_NATIONALE and selected_location:
                location = models.LocalCommission.objects.filter(id=int(selected_location)).first()
                schools = models.School.objects.filter(local_commission=location)
            elif role == constants.ROLE_COMMISSION_LOCALE:
                schools = models.School.objects.filter(
                    local_commission=self.request.user.localcommission)

        if schools:
            schools = schools.order_by('name')

        context['locations_list'] = locations
        context['schools_list'] = schools
        if selected_school:
            context['selected_school'] = int(selected_school)
        if selected_location:
            context['selected_location'] = int(selected_location)
        context['ROLE_ECOLE'] = constants.ROLE_ECOLE
        context['ROLE_COMMISSION_LOCALE'] = constants.ROLE_COMMISSION_LOCALE
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        context['exam_name'] = constants.EXAM_CEPE
        return context


class TransfertConfirmView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.TransferRequest
    fields = []
    template_name = 'partials/transfert/transfert_confirm.html'
    permission_required = 'exams.confirm_transfert'
    context_object_name = 'transfert'

    def form_valid(self, form):
        transfert = form.save(False)

        enrollment = transfert.enrollment
        enrollment.school = transfert.school
        enrollment.save(update_fields=['school'])
        transfert.confirmed = True
        transfert.save()
        sweetify.success(self.request, title='Tranfert éffectué', text='Transfert validé avec succès')
        return HttpResponseRedirect(reverse('transferts'))


class SchoolStatisticsListView(mixins.PermissionRequiredMixin, CustomPaginatedListView):
    model = models.SchoolStatistics
    context_object_name = 'statistics'
    template_name = 'partials/statistics/statistics_list.html'
    permission_required = 'exams.view_schoolstatistics'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.search_fields = [
            'school__name',
            'school__identifier',
            'school__director',
        ]

    def get_queryset(self):
        year = custom_utils.get_selected_year(self.request)
        user = self.request.user
        qs = models.SchoolStatistics.objects.filter(year=year).select_related('school', 'school__local_commission')

        # Filter by user role
        if user.role == constants.ROLE_ECOLE:
            qs = qs.filter(school=user.school)
        elif user.role == constants.ROLE_COMMISSION_LOCALE:
            qs = qs.filter(school__local_commission=user.localcommission)

        qs = self.filter_by_search_value(qs)
        selected_school = self.request.GET.get('schools_select')
        selected_location = self.request.GET.get('locations_select')
        if selected_school == '0' or selected_school == 0:
            selected_school = ''
        if selected_location == '0' or selected_location == 0:
            selected_location = ''

        if selected_school and selected_school:
            qs = qs.filter(school__id=selected_school)
        elif user.role == constants.ROLE_COMMISSION_NATIONALE and selected_location and selected_location:
            qs = qs.filter(school__local_commission__id=selected_location)
        return qs

    def filter_by_search_value(self, queryset):
        search_value = self.request.GET.get('search')
        new_qs = self.model.objects.none()
        if self.search_fields and search_value:
            for field in self.search_fields:
                if not new_qs.exists():
                    filter_expr = {str(field) : search_value}
                    if not str(field).endswith('iexact'):
                        filter_expr = {f'{field}__icontains': search_value}
                    new_qs = queryset.filter(**filter_expr)
            return new_qs
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        role = self.request.user.role
        selected_school = self.request.GET.get('schools_select')
        selected_location = self.request.GET.get('locations_select')
        if selected_school == '0' or selected_school == 0:
            selected_school = ''
        if selected_location == '0' or selected_location == 0:
            selected_location = ''

        locations = None
        schools = None
        if role != constants.ROLE_ECOLE:
            if role == constants.ROLE_COMMISSION_NATIONALE:
                locations = models.LocalCommission.objects.all()

            if role == constants.ROLE_COMMISSION_NATIONALE and selected_location:
                location = models.LocalCommission.objects.filter(id=int(selected_location)).first()
                schools = models.School.objects.filter(local_commission=location)
            elif role == constants.ROLE_COMMISSION_LOCALE:
                schools = models.School.objects.filter(
                    local_commission=self.request.user.localcommission)

        if schools:
            schools = schools.order_by('name')

        context['locations_list'] = locations
        context['schools_list'] = schools
        if selected_school:
            context['selected_school'] = int(selected_school)
        if selected_location:
            context['selected_location'] = int(selected_location)
        context['ROLE_ECOLE'] = constants.ROLE_ECOLE
        context['ROLE_COMMISSION_LOCALE'] = constants.ROLE_COMMISSION_LOCALE
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        context['section'] = 'statistics'
        return context


class SchoolStatisticsUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.SchoolStatistics
    fields = ['levels_count', 'students_count', 'boys_count', 'girls_count']
    template_name = 'partials/statistics/statistics_edit.html'
    permission_required = 'exams.change_schoolstatistics'
    context_object_name = 'statistics'

    def form_valid(self, form):
        sweetify.success(self.request, title='Enregistré',
            text='Statistiques modifiées avec succès', persistent=True)
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('statistics_list')

    def get(self, request, *args, **kwargs):
        if bool(request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('statistics_list'))


@decorators.permission_required('exams.add_transferrequest')
def transfert_edit_view(request):
    instance = None
    if request.GET.get('pk'):
        instance = models.TransferRequest.objects.get(pk=request.GET.get('pk'))

    form = forms.TransferEditForm(request.user, instance=instance, data=request.POST or None)

    if request.method == 'POST':
        if form.is_valid():
            form = form.save(True)
            print(request.GET, request.POST)
            return HttpResponseRedirect(
                reverse(
                    'candidates',
                    args=[form.enrollment.exam]))

    enrollment_id = request.GET.get('enrollment_id')
    if enrollment_id and not request.GET.get('pk'):
        enrollment_qs = models.Enrollment.objects.filter(pk=enrollment_id)
        enrollment = enrollment_qs.first()
        form.fields['enrollment'].queryset = enrollment_qs
        form.fields['old_school'].queryset = models.School.objects.filter(pk=enrollment.school_id)
        form.fields['enrollment'].initial = enrollment
        form.fields['old_school'].initial = models.School.objects.filter(pk=enrollment.school_id).first()
        form.fields['school'].queryset = models.School.objects.none()

    context = {
        'form': form,
        'locations': models.LocalCommission.objects.all()
    }
    return render(request, 'partials/transfert/transfert_form.html', context)


class MockExamListStudentsView(mixins.PermissionRequiredMixin, CustomPaginatedListView):
    model = models.Enrollment
    context_object_name = 'enrollments'
    template_name = 'partials/mock_exams/mock_exam_students.html'
    permission_required = 'exams.edit_mock_exam_grade'

    def get_queryset(self):
        user = self.request.user
        year = custom_utils.get_selected_year(self.request)
        qs = models.Enrollment.candidates.get_candidates(
            year=year, user=user, exam=self.request.GET.get('exam'),
            confirmed=True).order_by('student__last_name', 'student__first_name')

        selected_school = self.request.GET.get('schools_select')
        selected_location = self.request.GET.get('locations_select')
        if selected_school == '0' or selected_school == 0:
            selected_school = ''
        if selected_location == '0' or selected_location == 0:
            selected_location = ''

        if selected_school and selected_school:
            qs = qs.filter(school__id=selected_school)
        elif user.role == constants.ROLE_COMMISSION_NATIONALE and selected_location:
            qs = qs.filter(school__local_commission__id=selected_location)

        search = self.request.GET.get('search')
        qs = qs.annotate(
            full_name=Concat('student__last_name', Value(' '), 'student__first_name', output_field=CharField())
        )

        if search:
            qs = qs.filter(
                Q(full_name__icontains=search) | Q(student__full_name_ar__icontains=search) \
                | Q(student__identifier__icontains=search) | Q(student__matricule__icontains=search)
            )

        return qs.only(
            'student__matricule', 'student__photo', 'student__identifier', 'student__last_name',
            'student__first_name', 'student__gender', 'student__full_name_ar',
            'student__birth_date', 'school__name', 'school__local_commission__id',
        ).order_by('student__last_name', 'student__first_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        context['exam'] = self.request.GET.get('exam')
        context['ROLE_ECOLE'] = constants.ROLE_ECOLE
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE

        role = self.request.user.role
        selected_school = self.request.GET.get('schools_select')
        selected_location = self.request.GET.get('locations_select')
        if selected_school == '0' or selected_school == 0:
            selected_school = ''
        if selected_location == '0' or selected_location == 0:
            selected_location = ''

        locations = None
        schools = None
        if role != constants.ROLE_ECOLE:
            if role == constants.ROLE_COMMISSION_NATIONALE:
                locations = models.LocalCommission.objects.all()

            if role == constants.ROLE_COMMISSION_NATIONALE and selected_location:
                location = models.LocalCommission.objects.filter(id=int(selected_location)).first()
                schools = models.School.objects \
                    .annotate(students=Count('enrollment', distinct=True)) \
                    .filter(local_commission=location, students__gte=1)
            elif role == constants.ROLE_COMMISSION_LOCALE:
                schools = models.School.objects \
                    .annotate(students=Count('enrollment', distinct=True)) \
                    .filter(students__gte=1, local_commission=self.request.user.localcommission)

        if schools:
            schools = schools.order_by('name')

        context['locations_list'] = locations
        context['schools_list'] = schools
        if selected_school:
            context['selected_school'] = int(selected_school)
        if selected_location:
            context['selected_location'] = int(selected_location)

        qs = self.get_queryset()
        context['stats'] = qs.aggregate(
            marked=Count('mock_average', filter=Q(mock_average__isnull=False) & Q(mock_average__gte=0)),
            not_marked=Count('mock_average', filter=Q(mock_average__isnull=True) | Q(mock_average=0)),
        )
        first_unmarked = self.get_queryset() \
            .filter(school__id=self.request.GET.get('schools_select')) \
            .filter(Q(mock_average__isnull=True) | Q(mock_average=0)).first()
        if first_unmarked:
            context['first_unmarked'] = first_unmarked.id
        context['students_count'] = self.get_queryset().count()
        return context


class MockExamGradeEdit(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.Enrollment
    fields = ['student', 'school', 'mock_average']
    template_name = 'partials/grade/mock_exam_grade.html'
    permission_required = 'exams.edit_mock_exam_grade'

    def get_queryset(self):
        year = custom_utils.get_selected_year(self.request)
        return super().get_queryset().filter(
            school__id=self.request.GET.get('school'),
            active=True, year=year, confirmed=True)

    def form_valid(self, form):
        enrollment = form.save(True)
        enrollment = custom_utils.update_annual_average(enrollment)
        enrollment.save(update_fields=['gen_average'])
        if bool(self.request.POST.get('one_only')) == False:
            next_student = self.get_queryset().filter(
                school=enrollment.school_id, active=True,
                year=enrollment.year).filter(mock_average__isnull=True).first()
            if next_student:
                return HttpResponseRedirect(f"{reverse('mock_exam_grade_edit', args=[next_student.pk])}?exam={next_student.exam}&school={enrollment.school_id}")
        return HttpResponseRedirect(f"{reverse('mock_exam_students')}?exam={ enrollment.exam }&show_msg=1&schools_select={enrollment.school_id}&locations_select={enrollment.school.local_commission_id}")

    def get_context_data(self, **kwargs: Any):
        context = super().get_context_data(**kwargs)
        enrollment = self.get_object()
        form = context['form']
        form.fields['student'].queryset = models.Student.objects.filter(pk=enrollment.student_id)
        form.fields['school'].queryset = models.School.objects.filter(pk=enrollment.school_id)
        aggregated = self.get_queryset().aggregate(
            students_marked=Count('id', filter=Q(mock_average__isnull=False), distinct=True),
            students_count=Count('id', distinct=True),
        )
        context['stats'] = aggregated
        return context


class MockExamSchoolsListView(mixins.PermissionRequiredMixin, CustomPaginatedListView):
    model = models.School
    context_object_name = 'schools'
    template_name = 'partials/mock_exams/mock_exam_schools.html'
    permission_required = 'exams.view_school'

    def get_exam(self):
        exam = self.request.GET.get('exam')
        if (exam and exam.lower() in constants.EXAMS_LIST):
            return exam.lower()
        return constants.EXAM_CEPE

    def get_queryset(self):
        user = self.request.user
        year = custom_utils.get_selected_year(self.request)
        exam = self.get_exam()

        # Get schools filtered by user role
        qs = models.School.objects.get_for_user(user)

        # Filter by location if specified
        selected_location = self.request.GET.get('locations_select')
        if selected_location and selected_location != '0':
            qs = qs.filter(local_commission__id=selected_location)

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            qs = qs.filter(
                Q(name__icontains=search) |
                Q(identifier__icontains=search) |
                Q(director__icontains=search)
            )

        # Annotate with center information and student counts
        qs = qs.annotate(
            candidates_count=Count('enrollment',
                filter=Q(enrollment__active=True) &
                       Q(enrollment__year=year) &
                       Q(enrollment__exam=exam),
                distinct=True),
            candidates_marked_mock_exam=Count(
                'enrollment',
                filter=Q(enrollment__mock_average__gte=0) &
                       Q(enrollment__mock_average__isnull=False) &
                       Q(enrollment__confirmed=True) &
                       Q(enrollment__year=year) &
                       Q(enrollment__exam=exam),
                distinct=True
            ),
            center_name=Subquery(
                models.Center.objects.filter(
                    year=year,
                    exam=exam,
                    center_schools=OuterRef('pk')
                ).values('school__name')[:1]
            ),
        )

        return qs.filter(candidates_count__gte=1).order_by('name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['exam'] = self.get_exam()
        context['ROLE_ECOLE'] = constants.ROLE_ECOLE
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE

        # Add locations list for filtering
        if self.request.user.role == constants.ROLE_COMMISSION_NATIONALE:
            context['locations_list'] = models.LocalCommission.objects.all()

        # Get selected location
        selected_location = self.request.GET.get('locations_select')
        if selected_location and selected_location != '0':
            context['selected_location'] = int(selected_location)

        return context


@decorators.permission_required('exams.view_school')
def mock_exam_school_candidates_pdf(request, school_id, exam):
    user = request.user
    year = custom_utils.get_selected_year(request)
    school_id = int(school_id)
    school = None
    candidates = None
    exam = custom_utils.exam_or_default(exam)

    # Get the school
    school = get_object_or_404(models.School, id=school_id)

    # Get candidates for this school with mock exam grades
    candidates = models.Enrollment.objects.filter(
        school=school,
        year=year,
        exam=exam,
        active=True,
        confirmed=True
    ).select_related('student', 'center', 'school').order_by('student__last_name', 'student__first_name')

    # Create filename and directory for PDF
    filename = f'examen_blanc_{school.name}_{exam}_{user.id}'
    pdf_path = os.path.join('static', 'pdf')
    if not os.path.exists(pdf_path):
        os.makedirs(pdf_path)

    # Customize filename based on user role
    if user.role == constants.ROLE_ECOLE:
        filename = f'examen_blanc_{school.name}_{exam}'
    elif user.role == constants.ROLE_COMMISSION_LOCALE:
        filename = f'examen_blanc_{school.name}_{exam}_{user.localcommission.id}'

    # Generate the PDF
    pdf_file = os.path.join(pdf_path, f'{filename}.pdf')
    pdf = reports.FicheReportExamenBlanc(queryset=candidates, exam=exam, year=year, orientation='L')
    pdf.output(pdf_file)

    return FileResponse(
        open(pdf_file, 'rb'), as_attachment=True,
        content_type='application/pdf')


@decorators.permission_required('exams.view_grade')
def mock_exam_import_export_view(request):
    exam = custom_utils.clean_exam_selected(request)
    form = forms.ExcelUploadForm()

    if bool(request.htmx):
        template_name = 'partials/mock_exam/mock_exam_import_export.html'
    else:
        template_name = 'full_template.html'

    context = {
        'active_year': custom_utils.get_selected_year(request).short_name,
        'form': form,
        'selected_exam': exam,
        'user': request.user,
        'ROLE_COMMISSION_NATIONALE': constants.ROLE_COMMISSION_NATIONALE,
        'request': request
    }

    if not bool(request.htmx):
        context['template_name'] = 'partials/mock_exams/mock_exam_import_export.html'

    add_schools_and_locations_to_context(request, context, exam)
    return render(request, template_name, context)


@decorators.permission_required('exams.view_grade')
def mock_exam_import_view(request):
    exam = custom_utils.clean_exam_selected(request)
    form = forms.ExcelUploadForm(request.POST or None, files=request.FILES or None)

    if request.method == 'POST' and form.is_valid():
        # Save the uploaded file temporarily
        file = request.FILES['excel_file']
        school_id = request.POST.get('school')

        if school_id:
            # Save the file to a temporary location
            fs = FileSystemStorage(location=os.path.join('static', 'temp'))
            filename = fs.save(f"mock_exam_import_{request.user.id}.xlsx", file)
            file_path = fs.path(filename)

            # Redirect to preview page
            return HttpResponseRedirect(f"{reverse('mock_exam_import_preview')}?exam={exam}&school={school_id}&file={filename}")

    if bool(request.htmx):
        template_name = 'partials/mock_exam/mock_exam_import.html'
    else:
        template_name = 'full_template.html'

    context = {'active_year': custom_utils.get_selected_year(request).short_name}
    context['import'] = True
    context['form'] = form
    context['action'] = f"{reverse('mock_exam_import')}?exam={exam}"
    context['selected_exam'] = exam
    if not bool(request.htmx):
        context['template_name'] = 'partials/mock_exam/mock_exam_import.html'
    add_schools_and_locations_to_context(request, context, exam)
    return render(request, template_name, context)


@decorators.permission_required('exams.view_grade')
def mock_exam_import_preview_view(request):
    exam = custom_utils.clean_exam_selected(request)
    school_id = request.GET.get('school')
    file_name = request.GET.get('file')

    if not school_id or not file_name:
        return HttpResponseRedirect(f"{reverse('mock_exam_import')}?exam={exam}")

    # Load the Excel file
    file_path = os.path.join('static', 'temp', file_name)
    if not os.path.exists(file_path):
        sweetify.error(request, 'Erreur', text='Fichier non trouvé', persistent=True)
        return HttpResponseRedirect(f"{reverse('mock_exam_import')}?exam={exam}")

    # Get the school
    school = models.School.objects.filter(id=school_id).first()
    if not school:
        sweetify.error(request, 'Erreur', text='École non trouvée', persistent=True)
        return HttpResponseRedirect(f"{reverse('mock_exam_import')}?exam={exam}")

    # Load workbook and get preview data
    preview_data = []
    try:
        wb = load_workbook(file_path)
        ws = wb.active

        # Get all students from the school
        year = custom_utils.get_selected_year(request)
        students_dict = {}
        enrollments = models.Enrollment.objects.filter(
            school=school, year=year, exam=exam, active=True, confirmed=True
        ).select_related('student')

        for enrollment in enrollments:
            students_dict[enrollment.table_num] = {
                'enrollment': enrollment,
                'name': enrollment.student.get_full_name(),
                'current_average': enrollment.mock_average or 0
            }

        # Process Excel data
        for i, row in enumerate(ws.iter_rows(values_only=True)):
            if i == 0:  # Skip header row
                continue

            if len(row) >= 2:  # Ensure row has at least code and average
                student_code = str(row[0])
                try:
                    new_average = float(row[1])
                    if new_average < 0 or new_average > 20:
                        new_average = None  # Invalid average
                except (ValueError, TypeError):
                    new_average = None  # Invalid average

                student_info = students_dict.get(student_code)
                if student_info and new_average is not None:
                    preview_data.append({
                        'table_num': student_code,
                        'name': student_info['name'],
                        'current_average': student_info['current_average'],
                        'new_average': new_average,
                        'enrollment_id': student_info['enrollment'].id
                    })
    except Exception as e:
        sweetify.error(request, 'Erreur', text=f'Erreur lors de la lecture du fichier: {str(e)}', persistent=True)
        return HttpResponseRedirect(f"{reverse('mock_exam_import')}?exam={exam}")

    # Handle form submission (confirmation)
    if request.method == 'POST':
        # Update the mock averages
        enrollment_ids = request.POST.getlist('enrollment_id')
        new_averages = request.POST.getlist('new_average')

        if enrollment_ids and new_averages and len(enrollment_ids) == len(new_averages):
            updated_count = 0
            for i, enrollment_id in enumerate(enrollment_ids):
                try:
                    enrollment = models.Enrollment.objects.get(id=enrollment_id)
                    new_average = float(new_averages[i])
                    if 0 <= new_average <= 20:  # Validate average
                        enrollment.mock_average = new_average
                        # Update general average
                        enrollment = custom_utils.update_annual_average(enrollment)
                        enrollment.save(update_fields=['mock_average', 'gen_average'])
                        updated_count += 1
                except (models.Enrollment.DoesNotExist, ValueError, TypeError):
                    continue

            # Delete the temporary file
            if os.path.exists(file_path):
                os.remove(file_path)

            sweetify.success(request, 'Fait', text=f'{updated_count} moyennes mises à jour', persistent=True)
            return HttpResponseRedirect(f"{reverse('mock_exam_students')}?exam={exam}")

    if bool(request.htmx):
        template_name = 'partials/mock_exam/mock_exam_import_preview.html'
    else:
        template_name = 'full_template.html'

    context = {
        'active_year': custom_utils.get_selected_year(request).short_name,
        'preview_data': preview_data,
        'school': school,
        'exam': exam,
        'file_name': file_name,
        'action': f"{reverse('mock_exam_import_preview')}?exam={exam}&school={school_id}&file={file_name}"
    }

    if not bool(request.htmx):
        context['template_name'] = 'partials/mock_exam/mock_exam_import_preview.html'

    return render(request, template_name, context)


@decorators.permission_required('exams.view_grade')
def mock_exam_excel_view(request):
    # Create a new workbook and add a worksheet
    exam = custom_utils.clean_exam_selected(request)
    wb = Workbook()
    ws = wb.active

    # Add column headers
    ws['A1'] = 'CODE_ELEVE'
    ws['B1'] = 'MOYENNE_EXAMEN_BLANC'
    ws['C1'] = 'NOM_ET_PRENOMS'

    school_id = request.POST.get('school')
    if not school_id:
        return HttpResponseRedirect(reverse('mock_exam_export'))

    # Get the school and its students
    school = models.School.objects.filter(id=school_id).first()
    if not school:
        return HttpResponseRedirect(reverse('mock_exam_export'))

    # Get enrollments for the school
    year = custom_utils.get_selected_year(request)
    enrollments = models.Enrollment.objects.filter(
        school=school, year=year, exam=exam, active=True, confirmed=True
    ).select_related('student').order_by('student__last_name', 'student__first_name')

    # Add data rows
    for enrollment in enrollments:
        ws.append([
            enrollment.table_num,
            enrollment.mock_average or '',
            enrollment.student.get_full_name()
        ])

    # Create response with Excel file
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename=moyennes_examen_blanc_{school.name}_{exam}.xlsx'
    wb.save(response)

    return response


def add_schools_and_locations_to_context(request, context, exam):
    """Helper function to add schools and locations to context"""
    user = request.user
    year = custom_utils.get_selected_year(request)

    # Get schools based on user role
    schools = models.School.objects.get_for_user(user).filter(
        enrollment__year=year,
        enrollment__exam=exam,
        enrollment__active=True,
        enrollment__confirmed=True
    ).distinct().order_by('name')

    selected_location = context.get('request', {}).GET.get('locations_select')
    if (user.role != constants.ROLE_COMMISSION_NATIONALE) or (selected_location):
        context['schools'] = schools

    # Add locations if user is national commission
    if user.role == constants.ROLE_COMMISSION_NATIONALE:
        context['locations_list'] = models.LocalCommission.objects.all()

    # Get selected location
    if selected_location and selected_location != '0':
        context['selected_location'] = int(selected_location)


@decorators.permission_required('exams.view_school')
def centers_list_pdf(request):
    user = request.user
    year = custom_utils.get_selected_year(request)
    exam = request.GET.get('exam')
    centers = models.Center.objects \
        .get_for_year(user=user, year=year, exam=exam) \
        .order_by('school__local_commission__location',
                  'school__drena',
                  'school__iepp',
                  'school__drena_obj')

    location = request.GET.get('commission')
    if location:
        centers = centers.filter(school__local_commission__id=location)

    filename = f'centres_cherifla_{user.id}'
    pdf_path = os.path.join('static', 'pdf')
    if not os.path.exists(pdf_path):
        os.makedirs(pdf_path)
    location = None

    if user.role == constants.ROLE_COMMISSION_LOCALE:
        filename = f'centres_localite_{request.user.localcommission.id}'
        location = request.user.localcommission

    pdf_file = os.path.join(pdf_path, f'{filename}.pdf')
    pdf = reports.CentersListPDF(orientation='L')
    pdf.add_content(location=location, user=user, queryset=centers)
    pdf.output(pdf_file)

    return FileResponse(
        open(pdf_file, 'rb'), as_attachment=True,
            content_type='application/pdf')


class CorrectionCentersListView(BaseHTMXView, generic.ListView):
    model = models.CorrectionCenter
    template_name = 'partials/center/correction_centers.html'
    context_object_name = 'correction_centers'

    def get_queryset(self):
        exam = self.request.GET.get('exam')
        year = custom_utils.get_selected_year(self.request)
        return super().get_queryset().filter(exam=exam, year=year).annotate(
            candidates=Count(
                'center__enrollment',
                filter=Q(center__enrollment__confirmed=True) & \
                       Q(center__enrollment__active=True),
                distinct=True)
        )

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(*args, **kwargs)
        context['exam_name'] = self.request.GET.get('exam')
        return context


class CorrectionCenterCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.CorrectionCenter
    form_class = forms.CorrectionCenterForm
    template_name = 'partials/center/correction_center_form.html'
    permission_required = 'exams.add_correctioncenter'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        form = context['form']
        form.fields['exam'].initial = self.request.GET.get('exam')
        form.fields['centers'].queryset = models.Center.objects.none()
        return context

    def form_valid(self, form):
        obj = form.save(False)
        obj.year = custom_utils.get_selected_year(self.request)
        obj.save()
        obj.locations.set(models.LocalCommission.objects.filter(
            pk__in=self.request.POST.getlist('location_filter'))
        )
        selected_centers_list = self.request.POST.getlist('centers')
        selected_centers = models.Center.objects.filter(
            id__in=selected_centers_list,
            correction_center__isnull=True)


        centers_to_update = []
        for center in selected_centers:
            center.correction_center = obj
            centers_to_update.append(center)

        models.Center.objects.bulk_update(centers_to_update, fields=['correction_center'])
        # selected_centers.update(correction_center=obj)
        return HttpResponseRedirect(f"{reverse('correction_centers')}?exam={obj.exam}")


class CorrectionCenterUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.CorrectionCenter
    form_class = forms.CorrectionCenterForm
    template_name = 'partials/center/correction_center_form.html'
    permission_required = 'exams.add_correctioncenter'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        form = context['form']
        locations = self.get_object().locations.all()
        form.fields['centers'].queryset = models.Center.objects.filter(
            location__in=locations
        )

        obj = self.get_object()
        form.fields['exam'].choices = (
            (obj.exam, obj.exam.upper()),
        )
        form.fields['correction_center'].queryset = models.LocalCommission.objects.filter(
            pk=obj.correction_center.id)
        context['correction_centers'] = obj.center_set \
            .select_related('school', 'location') \
            .only('identifier', 'school__name', 'location__location')
        return context

    def form_valid(self, form):
        obj = form.save(True)
        selected_centers_list = self.request.POST.get('centers')
        if selected_centers_list:
            selected_centers = models.Center.objects.filter(
                id__in=selected_centers_list,
                correction_center__isnull=True)
            selected_centers.update(correction_center=obj)
        return HttpResponseRedirect(f"{reverse('correction_centers')}?exam={obj.exam}")


class LocationCentersForCorrectionView(mixins.PermissionRequiredMixin, generic.TemplateView):
    template_name = 'partials/commissions/commission_centers_m2m.html'
    permission_required = 'exams.view_correctioncenter'

    def get_context_data(self, **kwargs: Any):
        context = super().get_context_data(**kwargs)
        locations_ids = self.request.GET.getlist('location_filter')
        locations = models.LocalCommission.objects.filter(pk__in=locations_ids)

        year = custom_utils.get_selected_year(self.request)
        exam = self.request.GET.get('exam')
        form = forms.CorrectionCentersFieldForm()
        form.fields['centers'].queryset = models.Center.objects \
            .filter(location__pk__in=locations_ids) \
            .filter(year=year, exam=exam, correction_center__isnull=True)
        context['form'] = form
        return context


class CorrectionCentersCentersListView(mixins.PermissionRequiredMixin, generic.TemplateView):
    template_name = 'partials/center/correction_centers_centers_list.html'
    permission_required = 'exams.view_correctioncenter'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        correction_center_id = self.request.GET.get('correction_center')
        correction_center = models.CorrectionCenter.objects.get(pk=correction_center_id)
        centers = correction_center.center_set.annotate(
            candidates=Count('enrollment', filter=Q(enrollment__confirmed=True), distinct=True)
        ).select_related('school', 'location').only(
            'identifier', 'school__name', 'location__location'
        ).order_by('identifier', 'school__name')

        context['correction_center'] = correction_center
        context['centers'] = centers
        context['exam'] = correction_center.exam
        return context


class StudentsUnmarkedListView(mixins.PermissionRequiredMixin, CustomPaginatedListView):
    model = models.Enrollment
    template_name = 'partials/grade/students_unmarked.html'
    context_object_name = 'enrollments'
    permission_required = 'exams.view_results'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        role = self.request.user.role
        selected_school = self.request.GET.get('schools_select')
        selected_location = self.request.GET.get('locations_select')
        if selected_school == '0' or selected_school == 0:
            selected_school = ''
        if selected_location == '0' or selected_location == 0:
            selected_location = ''

        locations = None
        if role != constants.ROLE_ECOLE:
            if role == constants.ROLE_COMMISSION_NATIONALE:
                locations = models.LocalCommission.objects.all()

            if role == constants.ROLE_COMMISSION_NATIONALE and selected_location:
                location = models.LocalCommission.objects.filter(id=int(selected_location)).first()


        context['locations_list'] = locations
        if selected_location:
            context['selected_location'] = int(selected_location)
        if self.request.GET.get('search'):
            context['search'] = self.request.GET.get('search')
        if self.request.GET.get('per_page'):
            context['per_page'] = self.request.GET.get('per_page')
        if self.request.GET.get('search'):
            context['result_found'] = context.get('object_count')

        context['exam'] = self.request.GET.get('exam')
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        return context

    def get_queryset(self):
        user = self.request.user

        exam = self.request.GET.get('exam')
        year = custom_utils.get_selected_year(self.request)
        qs = models.Enrollment.candidates.get_candidates(
            user=user, year=year, confirmed=True, select_related=False,
            exam=exam
        ).filter(center__isnull=False, table_num__isnull=False)

        if user.role == constants.ROLE_COMMISSION_LOCALE and user.localcommission:
            qs = qs.filter(center__location=user.localcommission)

        qs = qs.filter(Q(average__isnull=True) | Q(average=0))
        selected_location = self.request.GET.get('locations_select')
        if selected_location == '0' or selected_location == 0:
            selected_location = ''

        if user.role == constants.ROLE_COMMISSION_NATIONALE and selected_location:
            qs = qs.filter(school__local_commission__id=selected_location)

        search = self.request.GET.get('search')
        if search:
            qs = qs.filter(
                Q(table_num=search)
            )

        return qs.select_related('center__school', 'center__location').only(
            'table_num', 'center__id', 'center__identifier', 'center__school__name',
            'center__location__location'
        ).order_by('center__id')


class StudentsGradesStatusListView(mixins.PermissionRequiredMixin, CustomPaginatedListView):
    model = models.Enrollment
    template_name = 'partials/grade/students_grades_status.html'
    context_object_name = 'enrollments'
    permission_required = 'exams.view_grade'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        exam = custom_utils.clean_exam_selected(self.request)
        year = custom_utils.get_selected_year(self.request)
        context['active_year'] = year.short_name
        context['exam'] = exam

        # Get centers for dropdown
        centers = models.Center.objects.get_for_year(
            year=year, user=self.request.user, exam=exam, annotate=False
        ).order_by('identifier')
        context['centers_list'] = centers

        # Get selected center
        selected_center = self.request.GET.get('center')
        if selected_center:
            context['selected_center'] = int(selected_center)

        # Add search and pagination context
        if self.request.GET.get('search'):
            context['search'] = self.request.GET.get('search')
        if self.request.GET.get('per_page'):
            context['per_page'] = self.request.GET.get('per_page')
        if self.request.GET.get('search'):
            context['result_found'] = context.get('object_count')

        return context

    def get_queryset(self):
        user = self.request.user
        exam = custom_utils.clean_exam_selected(self.request)
        year = custom_utils.get_selected_year(self.request)

        # Start with empty queryset
        qs = models.Enrollment.objects.none()

        # Only get enrollments if a center is selected
        selected_center = self.request.GET.get('center')
        if selected_center:
            qs = models.Enrollment.candidates.get_candidates(
                user=user, year=year, confirmed=True, exam=exam, select_related=False
            ).filter(center_id=selected_center, table_num__isnull=False)

            # Apply search filter if provided
            search = self.request.GET.get('search')
            if search:
                qs = qs.filter(Q(table_num__icontains=search))

        # Annotate with is_marked status
        qs = qs.annotate(
            is_marked=Q(average__isnull=False) & Q(average__gt=0)
        )

        return qs.only('id', 'table_num', 'exam') \
            .order_by('table_num')


class LocationListView(mixins.PermissionRequiredMixin, BaseHTMXView, generic.ListView):
    model = models.Location
    context_object_name = 'locations'
    template_name = 'partials/locations/location_list.html'
    permission_required = 'exams.view_location'


class LocationCenterStatsView(mixins.PermissionRequiredMixin, BaseHTMXView, generic.ListView):
    model = models.LocalCommission
    template_name = 'partials/center/location_center_stats.html'
    context_object_name = 'locations'
    permission_required = 'exams.view_localcommission'

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        year = custom_utils.get_selected_year(self.request)
        user = self.request.user

        # Get all locations
        queryset = self.model.objects.all().order_by('location')

        # Filter by user role if needed
        if user.role == constants.ROLE_COMMISSION_LOCALE:
            queryset = queryset.filter(id=user.localcommission.id)

        # Annotate with student counts
        queryset = queryset.annotate(
            total_students=Count(
                'school__enrollment',
                filter=Q(school__enrollment__year=year) &
                       Q(school__enrollment__exam=exam) &
                       Q(school__enrollment__active=True) &
                       Q(school__enrollment__confirmed=True),
                distinct=True
            ),
            students_with_center=Count(
                'school__enrollment',
                filter=Q(school__enrollment__year=year) &
                       Q(school__enrollment__exam=exam) &
                       Q(school__enrollment__active=True) &
                       Q(school__enrollment__confirmed=True) &
                       Q(school__enrollment__center__isnull=False),
                distinct=True
            )
        )

        # Add a calculated field for students without center
        for location in queryset:
            location.students_without_center = location.total_students - location.students_with_center
            location.status = 'Complet' if location.students_without_center == 0 and location.total_students > 0 else 'Incomplet'

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        exam = custom_utils.clean_exam_selected(self.request)
        year = custom_utils.get_selected_year(self.request)

        # Add summary statistics
        queryset = self.get_queryset()
        total_students = sum(location.total_students for location in queryset)
        students_with_center = sum(location.students_with_center for location in queryset)
        students_without_center = total_students - students_with_center

        context['stats'] = {
            'total_students': total_students,
            'students_with_center': students_with_center,
            'students_without_center': students_without_center,
            'completion_percentage': (students_with_center / total_students * 100) if total_students > 0 else 0
        }

        context['active_year'] = year.short_name
        context['selected_exam'] = exam
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        return context


class LocationCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.Location
    fields = ['name']
    template_name = 'components/simple_form.html'
    permission_required = 'exams.add_location'
    success_url = reverse_lazy('drena')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Ajouter DRENA'
        return context


class LocationUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.Location
    fields = ['name']
    template_name = 'components/simple_form.html'
    permission_required = 'exams.change_location'
    success_url = reverse_lazy('drena')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = f'Editer DRENA: {self.get_object()}'
        return context


class StaffListView(mixins.PermissionRequiredMixin, CustomPaginatedListView):
    template_name = 'partials/staff/staff_list.html'
    permission_required = 'exams.view_staff'
    model = models.Staff

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.search_fields = ['first_name', 'last_name', 'phone', 'code']

    def get_paginate_by(self, queryset):
        per_page = self.request.GET.get('per_page')
        if per_page:
            return int(per_page)
        return 10

    def get_context_data(self, **kwargs: Any):
        context = super().get_context_data(**kwargs)
        context['active_year'] = custom_utils.get_selected_year(self.request).short_name
        queryset = self.get_queryset()
        user = self.request.user
        year = custom_utils.get_selected_year(self.request)
        if queryset.exists():
            aggregated = queryset.aggregate(
                pending=Count('id', filter=Q(status=models.Staff.STATUS_PENDING) & (Q(location__in=user.locations.all()) if user.role == constants.ROLE_DELEGATE else Q()), distinct=True),
                manufactured=Count('id', filter=Q(status=models.Staff.STATUS_MANUFACTURED) & (Q(location__in=user.locations.all()) if user.role == constants.ROLE_DELEGATE else Q()), distinct=True),
                shipped=Count('id', filter=Q(status=models.Staff.STATUS_SHIPPED)  & (Q(location__in=user.locations.all()) if user.role == constants.ROLE_DELEGATE else Q()), distinct=True),
            )
            context['stats'] = aggregated
        return context

    def get_queryset(self):
        year = custom_utils.get_selected_year(self.request)
        queryset = super().get_queryset().filter(year=year)
        user = self.request.user
        if user.role == constants.ROLE_DELEGATE:
            queryset = queryset.filter(location__in=user.locations.all())

        search_value = self.request.GET.get('search')
        if search_value:
            queryset = queryset.annotate(full_name=Concat('last_name', Value(' '), 'first_name', output_field=CharField()))
            queryset = queryset.filter(Q(full_name__icontains=search_value))

        return queryset.only(
            'code', 'last_name', 'first_name',
            'birth_date', 'birth_place', 'photo',
            'status', 'location', 'gender', 'job',
            'year__name', 'location__name', 'school__name'
        ).select_related('location', 'year', 'school') \
        .order_by('-date_created')


class StaffCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = models.Staff
    fields = [
        'location', 'school', 'job', 'education', 'date_hired',
        'gender', 'last_name', 'first_name',
        'birth_date', 'birth_place',
        'father', 'mother',
        'phone', 'address', 'photo',
    ]
    template_name = 'partials/staff/staff_form.html'
    success_url = reverse_lazy('staff')
    permission_required = 'exams.add_staff'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Ajouter un membre du personnel'

        user = self.request.user
        locations = models.Location.objects.all()
        if user.role == constants.ROLE_DELEGATE:
            locations = user.locations.all()
        context['form'].fields['location'].queryset = locations
        context['form'].fields['school'].queryset = models.School.objects.none()
        context['col_width'] = 'col-md-6'
        return context

    def generate_sequential_code(self):
        staff_count = models.Staff.objects.count()
        staff_code = f"{str(staff_count).zfill(3)}"
        while models.Staff.objects.filter(code=staff_code).exists():
            staff_count += 1
            staff_code = f"{str(staff_count).zfill(3)}"
        return staff_code

    def form_valid(self, form):
        staff = form.save(False)
        staff.year = custom_utils.get_selected_year(self.request)
        staff.code = self.generate_sequential_code()
        staff.agent = self.request.user
        staff.save()
        return HttpResponseRedirect(reverse_lazy('staff'))


class StaffUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.Staff
    fields = [
        'location', 'school', 'job', 'education', 'date_hired',
        'gender', 'last_name', 'first_name', 'gender',
        'birth_date', 'birth_place',
        'father', 'mother',
        'phone', 'address', 'photo',
    ]
    template_name = 'partials/staff/staff_form.html'
    success_url = reverse_lazy('staff')
    permission_required = 'exams.change_staff'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = f'Modification: {self.get_object()}'
        context['col_width'] = 'col-md-6'

        user = self.request.user
        locations = models.Location.objects.all()
        if user.role == constants.ROLE_DELEGATE:
            locations = user.locations.all()
        context['form'].fields['location'].queryset = locations
        selected_location = self.get_object().location_id
        context['form'].fields['school'].queryset = models.School.objects.filter(
            drena_obj__pk=selected_location)
        return context


def get_schools_by_drena(request):
    location_id = request.GET.get('location')
    schools = models.School.objects.filter(drena_obj__pk=location_id)
    return render(request, 'partials/staff/drena_schools.html', {'schools': schools})


def center_confirm_view(request):
    if request.method == 'POST':
        centers = models.Center.objects.get_for_year(
            user=request.user, year=custom_utils.get_selected_year(request),
            exam=request.POST.get('exam'))
        centers = centers.filter(complete=False)

        for center in centers:
            custom_utils.confirm_center(center)

        sweetify.success(request, title='Terminé', text='Veuillez vérifier les centres confirmés')
        return HttpResponseRedirect(reverse('centers'))

    context = {'exam': custom_utils.clean_exam_selected(request)}
    return render(request, 'partials/center/center_confirm.html', context)


class SchoolCentersListView(mixins.PermissionRequiredMixin, generic.ListView):
    model = models.School
    context_object_name = 'schools'
    permission_required = 'exams.view_school'

    def get_exam(self):
        exam = self.request.GET.get('exam')
        if (exam and exam.lower() in constants.EXAMS_LIST):
            return exam.lower()
        return constants.EXAM_CEPE

    def get_template_names(self):
        if self.request.htmx:
            return ['partials/school/school_centers_list.html']
        return ['full_template.html']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = custom_utils.get_selected_year(self.request)
        context['active_year'] = year.short_name
        context['exam'] = custom_utils.clean_exam_selected(self.request)
        context['ROLE_COMMISSION_LOCALE'] = constants.ROLE_COMMISSION_LOCALE
        context['ROLE_COMMISSION_NATIONALE'] = constants.ROLE_COMMISSION_NATIONALE
        context['per_page'] = self.request.GET.get('per_page')
        locations = models.LocalCommission.objects.all()
        if self.request.user.role == constants.ROLE_COMMISSION_LOCALE:
            locations = locations.filter(id=self.request.user.localcommission.id)
        context['locations'] = locations
        selected_location = self.request.GET.get('location')
        if selected_location:
            selected_location = int(selected_location)

        context['selected_location'] = selected_location

        search = self.request.GET.get('search')
        if search:
            context['search'] = search
            context['result_found'] = self.get_queryset().count()

        return context

    def get_queryset(self):
        user = self.request.user
        exam = self.get_exam()
        year = custom_utils.get_selected_year(self.request)

        qs = models.School.objects.get_for_user(user)

        # Annotate with center information for the active year and exam
        qs = qs.annotate(
            has_center=Count('school_centers', filter=Q(school_centers__year=year) & Q(school_centers__exam=exam)),
            center_id=Subquery(
                models.Center.objects.filter(
                    year=year,
                    exam=exam,
                    center_schools=OuterRef('pk')
                ).values('id')[:1]
            ),
            center_identifier=Subquery(
                models.Center.objects.filter(
                    year=year,
                    exam=exam,
                    center_schools=OuterRef('pk')
                ).values('school__name')[:1]
            ),
            student_count=Count(
                'enrollment',
                filter=Q(enrollment__year=year) &
                       Q(enrollment__exam=exam) &
                       Q(enrollment__confirmed=True),
                distinct=True
            )
        ).filter(student_count__gte=1)

        location = self.request.GET.get('location')
        if location:
            qs = qs.filter(local_commission__id=location)

        search = self.request.GET.get('search')
        if search:
            qs = qs.filter(
                Q(name__icontains=search) |
                Q(name_ar__icontains=search) |
                Q(identifier__iexact=search)
            )

        return qs.order_by('name')

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))

    def get_paginate_by(self, queryset):
        page_param = self.request.GET.get('per_page')
        if page_param:
            return int(page_param)
        return 10


class SchoolCenterCancelView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.School
    template_name = 'partials/school/school_center_cancel_confirm.html'
    permission_required = 'exams.change_school'
    fields = []

    def get_success_url(self):
        exam = self.kwargs.get('exam')
        return f"{reverse('school_centers')}?exam={exam}"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.get_object()
        exam = self.kwargs.get('exam')
        year = custom_utils.get_selected_year(self.request)

        # Get the center for this school
        center = models.Center.objects.filter(
            year=year,
            exam=exam,
            center_schools=school
        ).first()

        context['center'] = center
        context['exam'] = exam
        return context

    def form_valid(self, form):
        school = self.get_object()
        exam = self.kwargs.get('exam')
        year = custom_utils.get_selected_year(self.request)

        # Find centers that have this school
        centers = models.Center.objects.filter(
            year=year,
            exam=exam,
            center_schools=school
        )

        # Remove the school from each center's center_schools
        for center in centers:
            print(str(center))
            center.center_schools.remove(school)

        sweetify.success(self.request, title='Fait',
            text='École retirée du centre avec succès', persistent=True)

        return HttpResponseRedirect(self.get_success_url())

    def get(self, request, *args, **kwargs):
        if bool(request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('school_centers'))

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))


class SchoolCenterAssignView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = models.School
    template_name = 'partials/school/school_center_assign_form.html'
    permission_required = 'exams.change_school'
    fields = []

    def get_success_url(self):
        exam = self.kwargs.get('exam')
        return f"{reverse('school_centers')}?exam={exam}"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['exam'] = self.kwargs.get('exam')
        context['locations'] = models.LocalCommission.objects.all()

        if self.request.user.role == constants.ROLE_COMMISSION_LOCALE:
            context['locations'] = models.LocalCommission.objects.filter(
                id=self.request.user.localcommission.id
            )

        return context

    def form_valid(self, form):
        school = self.get_object()
        exam = self.kwargs.get('exam')
        year = custom_utils.get_selected_year(self.request)
        center_id = self.request.POST.get('center')

        if center_id:
            center = models.Center.objects.filter(
                id=center_id,
                year=year,
                exam=exam
            ).first()

            if center:
                # Add the school to the center's center_schools many-to-many relationship
                center.center_schools.add(school)

                sweetify.success(self.request, title='Fait',
                    text='École assignée au centre avec succès', persistent=True)
            else:
                sweetify.error(self.request, title='Erreur',
                    text='Centre introuvable', persistent=True)

        return HttpResponseRedirect(self.get_success_url())

    def get(self, request, *args, **kwargs):
        if bool(request.htmx):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse('school_centers'))

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse('home'))


@decorators.login_required
def location_centers_for_school(request):
    """View to get centers for a selected location"""
    location_id = request.GET.get('location')
    exam = request.GET.get('exam', constants.EXAM_CEPE)
    year = custom_utils.get_selected_year(request)

    centers = models.Center.objects.none()

    if location_id:
        centers = models.Center.objects.filter(
            year=year,
            exam=exam,
            location_id=location_id
        ).select_related('school')

    return render(request, 'partials/school/location_centers_for_school.html', {'centers': centers})


class CenterDocumentsListView(BaseHTMXView, mixins.PermissionRequiredMixin, generic.ListView):
    model = models.Center
    template_name = 'partials/center/center_documents_list.html'
    context_object_name = 'centers'
    permission_required = 'exams.view_center'

    def get_queryset(self):
        exam = custom_utils.clean_exam_selected(self.request)
        year = custom_utils.get_selected_year(self.request)
        qs = models.Center.objects.filter(exam=exam, year=year) \
            .select_related('school') \
            .prefetch_related('room_set') \

        user = self.request.user
        if user and user.role == constants.ROLE_COMMISSION_LOCALE:
            qs = qs.filter(location=user.localcommission)

        return qs.only('id', 'identifier', 'school__name', 'school__name_ar')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['exam'] = custom_utils.clean_exam_selected(self.request)
        return context
